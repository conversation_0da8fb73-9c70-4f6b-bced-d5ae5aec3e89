package org.dromara.system.api.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.github.linpeilie.annotations.AutoMapping;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;


/**
 * 用户
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class RemoteUserVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 部门ID
     */
    private Long deptId;

    /**
     * 用户UUID
     */
    private String uuid;

    /**
     * 用户账号
     */
    private String userName;

    /**
     * 用户昵称
     */
    private String nickName;

    /**
     * 用户类型（sys_user系统用户）
     */
    private String userType;

    /**
     * 用户邮箱
     */
    private String email;

    /**
     * 国际电话区号(如+86)
     */
    private String countryCode;

    /**
     * 手机号码
     */
    private String phonenumber;

    /**
     * 用户性别（0男 1女 2未知）
     */
    private String sex;

    /**
     * 谷歌身份验证器密钥
     */
    private String googleSecretKey;

    /**
     * 帐号状态（0正常 1停用）
     */
    private String status;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 邀请码
     */
    private String inviteCode;

    /**
     * 语言
     */
    private String language;

    /**
     * 地点
     */
    private String location;

    /**
     * 系统环境
     */
    private String os;

    /**
     * 设备
     */
    private String device;

    /**
     * 租户ID
     */
    private String tenantId;

    /**
     * 交易受限到期时间
     */
    @AutoMapping(dateFormat = "yyyy-MM-dd HH:mm:ss")
    private String tradeLimitExpired;

    /**
     * 登录受限到期时间
     */
    @AutoMapping(dateFormat = "yyyy-MM-dd HH:mm:ss")
    private String loginLimitExpired;

    /**
     * 渠道名字
     */
    private String channelName;

    /**
     * 分页总条数
     */
    private Long total;

}
