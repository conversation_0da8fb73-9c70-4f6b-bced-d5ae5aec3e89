<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>org.dromara</groupId>
    <artifactId>ruoyi-common-alibaba-bom</artifactId>
    <version>${revision}</version>
    <packaging>pom</packaging>

    <description>
        ruoyi-common-alibaba-bom alibaba依赖项
    </description>

    <properties>
        <revision>2.4.0</revision>
        <spring-cloud-alibaba.version>2023.0.1.2</spring-cloud-alibaba.version>
        <sentinel.version>1.8.8</sentinel.version>
        <seata.version>2.3.0</seata.version>
        <nacos.client.version>2.5.1</nacos.client.version>
        <dubbo.version>3.3.4</dubbo.version>
        <dubbo-extensions.version>3.3.1</dubbo-extensions.version>
        <spring.context.support.version>1.0.11</spring.context.support.version>
    </properties>
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-alibaba-dependencies</artifactId>
                <version>${spring-cloud-alibaba.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.alibaba.nacos</groupId>
                <artifactId>nacos-client</artifactId>
                <version>${nacos.client.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.csp</groupId>
                <artifactId>sentinel-core</artifactId>
                <version>${sentinel.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.csp</groupId>
                <artifactId>sentinel-parameter-flow-control</artifactId>
                <version>${sentinel.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.csp</groupId>
                <artifactId>sentinel-datasource-extension</artifactId>
                <version>${sentinel.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.csp</groupId>
                <artifactId>sentinel-datasource-apollo</artifactId>
                <version>${sentinel.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.csp</groupId>
                <artifactId>sentinel-datasource-zookeeper</artifactId>
                <version>${sentinel.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.csp</groupId>
                <artifactId>sentinel-datasource-nacos</artifactId>
                <version>${sentinel.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.csp</groupId>
                <artifactId>sentinel-datasource-redis</artifactId>
                <version>${sentinel.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.csp</groupId>
                <artifactId>sentinel-datasource-consul</artifactId>
                <version>${sentinel.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.csp</groupId>
                <artifactId>sentinel-web-servlet</artifactId>
                <version>${sentinel.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.csp</groupId>
                <artifactId>sentinel-spring-cloud-gateway-adapter</artifactId>
                <version>${sentinel.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.csp</groupId>
                <artifactId>sentinel-transport-simple-http</artifactId>
                <version>${sentinel.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.csp</groupId>
                <artifactId>sentinel-annotation-aspectj</artifactId>
                <version>${sentinel.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.csp</groupId>
                <artifactId>sentinel-reactor-adapter</artifactId>
                <version>${sentinel.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.csp</groupId>
                <artifactId>sentinel-cluster-server-default</artifactId>
                <version>${sentinel.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.csp</groupId>
                <artifactId>sentinel-cluster-client-default</artifactId>
                <version>${sentinel.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.csp</groupId>
                <artifactId>sentinel-spring-webflux-adapter</artifactId>
                <version>${sentinel.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.csp</groupId>
                <artifactId>sentinel-api-gateway-adapter-common</artifactId>
                <version>${sentinel.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.csp</groupId>
                <artifactId>sentinel-spring-webmvc-v6x-adapter</artifactId>
                <version>${sentinel.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.csp</groupId>
                <artifactId>sentinel-dubbo-adapter</artifactId>
                <version>${sentinel.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.csp</groupId>
                <artifactId>sentinel-apache-dubbo-adapter</artifactId>
                <version>${sentinel.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.csp</groupId>
                <artifactId>sentinel-apache-dubbo3-adapter</artifactId>
                <version>${sentinel.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.seata</groupId>
                <artifactId>seata-spring-boot-starter</artifactId>
                <version>${seata.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.seata</groupId>
                <artifactId>seata-all</artifactId>
                <version>${seata.version}</version>
            </dependency>

            <!-- Apache Dubbo 配置 -->
            <dependency>
                <groupId>org.apache.dubbo</groupId>
                <artifactId>dubbo-spring-boot-starter</artifactId>
                <version>${dubbo.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.dubbo</groupId>
                <artifactId>dubbo-spring-boot-actuator</artifactId>
                <version>${dubbo.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.dubbo</groupId>
                <artifactId>dubbo</artifactId>
                <version>${dubbo.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.dubbo.extensions</groupId>
                <artifactId>dubbo-metadata-report-redis</artifactId>
                <version>${dubbo-extensions.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.spring</groupId>
                <artifactId>spring-context-support</artifactId>
                <version>${spring.context.support.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>
</project>
