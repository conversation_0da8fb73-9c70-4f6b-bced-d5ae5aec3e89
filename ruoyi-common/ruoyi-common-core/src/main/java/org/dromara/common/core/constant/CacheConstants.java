package org.dromara.common.core.constant;

/**
 * 缓存的key 常量
 *
 * <AUTHOR> Li
 */
public interface CacheConstants {

    /**
     * 在线用户 redis key
     */
    String ONLINE_TOKEN_KEY = "online_tokens:";

    /**
     * 参数管理 cache key
     */
    String SYS_CONFIG_KEY = "sys_config:";

    /**
     * 字典管理 cache key
     */
    String SYS_DICT_KEY = "sys_dict:";

    /**
     * 登录账户密码错误次数 redis key
     */
    String PWD_ERR_CNT_KEY = "pwd_err_cnt:";



    //***************************************************** KazePay **********************************************************/

    /**
     * 交易密码错误次数
     */
    String TRADE_ERROR_KEY = "meta_trade_error_key:";

    /**
     * 登录失败(密码) redis key
     */
    String LOGIN_FAIL_PASS_KEY = "meta_pass_login_fail:";

    /**
     * 登录失败(邮箱验证码) redis key
     */
    String LOGIN_FAIL_EMAIL_KEY = "meta_email_login_fail:";

    /**
     * 谷歌身份验证器(验证码) redis key
     */
    String LOGIN_FAIL_GOOGLE_KEY = "meta_google_login_fail:";

    /**
     * 操作用户余额锁
     */
    String COIN_BALANCE_LOCK_KEY = "coin_balance_lock:";

}
