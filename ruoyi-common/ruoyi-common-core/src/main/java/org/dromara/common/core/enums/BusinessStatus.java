package org.dromara.common.core.enums;

/**
 * KazePay业务状态
 */
public enum BusinessStatus {

    //todo 后续是否根据模块或业务进行前缀区别
    //moonbank
//    DECLINED("DECLINED",'0'),
//    APPROVED("APPROVED",'1'),
//    CONFIRM("CONFIRM",'1'),
//    PENDING("PENDING",'2'),
//    pending("pending",'2'),
//    posted("posted",'1'),
//    declined("declined",'0'),
//    vodi("vodi",'0'),
//    CANCEL("CANCEL",'0'),
//    OTHER("OTHER",'1'),
//
//
//    //vcc
//    FAIL("fail",'0'),
//    SUCCESS("success",'1'),
//    PADDING("processing",'2'),


    /**
     * 币种交易状态
     * PENDING（待审核） SUCCESS（已完成） REJECT（已拒绝）
     */
    COIN_PENDING("PENDING", "AsetCoinTxnStatus000"),
    COIN_SUCCESS("SUCCESS", "AsetCoinTxnStatus001"),
    COIN_REJECT("REJECT", "AsetCoinTxnStatus002"),


    /**
     * 优惠券状态
     * VALID（有效） USED（已使用） WRITE_OFF（核销） EXPIRED（过期） DEL（删除）
     */
    COUPON_VALID("VALID", "AsetCopnStatus000000"),
    COUPON_USED("USED", "AsetCopnStatus000001"),
    COUPON_WRITE_OFF("WRITE_OFF", "AsetCopnStatus000002"),
    COUPON_EXPIRED("EXPIRED", "AsetCopnStatus000003"),
    COUPON_DEL("DEL", "AsetCopnStatus000004");

    String name;
    String value;

    BusinessStatus(String name, String value) {
        this.name = name;
        this.value = value;
    }

    public static String getValueByName(String name){
        for(BusinessStatus ts: BusinessStatus.values()){
            if(ts.name.equals(name))
                return ts.value;
        }
        return null;
    }

    public String getName() {
        return this.name;
    }

    public String getValue() {
        return value;
    }
}
