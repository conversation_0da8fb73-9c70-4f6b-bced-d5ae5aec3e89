package org.dromara.common.core.enums;

/**
 * 优惠券枚举
 */
public enum CouponEnum {

    /**
     * 优惠券场景
     * NEW (开卡)  TOPUP (卡充值)  NODE-节点购买
     */
    COUPON_OPEN("NEW"),
    COUPON_RECHARGE( "TOPUP"),
    COUPON_NODE("NODE"),

    /**
     * 优惠券类型
     * D (折扣券)  M (满减券)
     */
    COUPON_DISCOUNT("D"),
    COUPON_FULL_DISCOUNT( "M");

    String value;

    CouponEnum(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }
}
