package org.dromara.common.core.enums;

/**
 * 字典类型
 */
public enum DictType {

    /**
     * 优惠券类型
     */
    ASET_COPN_TYPE("AsetCopnType"),
    /**
     * 优惠券场景
     */
    ASET_COPN_SCEN("AsetCopnScen"),
    /**
     * 优惠券状态
     */
    ASET_COPN_STATUS("AsetCopnStatus"),



    /**
     * 信用卡类型
     */
    CARD_TYPE("CardType"),
    /**
     * 信用卡等级
     */
    CARD_LEVEL("CardLevel"),



    /**
     * 节点类型
     */
    ACCT_NODE_TYPE("AcctNodeType"),



    /**
     * 开卡币交易类型
     */
    ASET_CODE_TXN_TYPE("AsetCodeTxnType"),



    /**
     * 币种交易状态
     */
    ASET_COIN_TXN_STATUS("AsetCoinTxnStatus");

    String value;

    DictType(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }
}
