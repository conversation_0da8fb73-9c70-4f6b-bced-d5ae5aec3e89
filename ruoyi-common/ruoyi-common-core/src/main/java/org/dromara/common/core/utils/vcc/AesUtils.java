
package org.dromara.common.core.utils.vcc;
import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.security.NoSuchAlgorithmException;
import java.util.Base64;

public class AesUtils {
    private static final Charset UTF_8 = StandardCharsets.UTF_8;
    private static final String AES = "AES";

    public static String aesEncrypt(String keyStr, String plaintext) {
        SecretKey key = newAesKey(keyStr);
        try {
            return aesEncrypt(key, plaintext);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public static String aesDecrypt(String keyStr, String ciphertext) {
        SecretKey key = newAesKey(keyStr);
        try {
            return aesDecrypt(key, ciphertext);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 生成符合AES要求的密钥, 长度为128位.
     */
    public static String generateAesKey() throws NoSuchAlgorithmException {
        KeyGenerator keyGenerator = KeyGenerator.getInstance(AES);
        SecretKey secretKey = keyGenerator.generateKey();
        return Base64.getEncoder().encodeToString(secretKey.getEncoded());
    }

    private static String aesEncrypt(SecretKey key, String plaintext) throws Exception {
        Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");
        cipher.init(Cipher.ENCRYPT_MODE, key);
        byte[] cipherBytes = cipher.doFinal(plaintext.getBytes(UTF_8));
        return Base64.getEncoder().encodeToString(cipherBytes);
    }

    private static String aesDecrypt(SecretKey key, String ciphertext) throws Exception {
        Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");
        cipher.init(Cipher.DECRYPT_MODE, key);
        byte[] cipherBytes = Base64.getDecoder().decode(ciphertext);
        return new String(cipher.doFinal(cipherBytes), UTF_8);
    }

    private static SecretKey newAesKey(String keyStr) {
        byte[] decodedKey = Base64.getDecoder().decode(keyStr);
        return new SecretKeySpec(decodedKey, AES);
    }
}
