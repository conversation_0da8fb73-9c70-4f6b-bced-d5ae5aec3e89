package org.dromara.common.redis.utils;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.util.Random;

@Component
public class RandomStrUtils {

    private static final String CHARS = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";

    @Autowired
    private StringRedisTemplate redisTemplate;

    public String generate(int length) {

        Random random = new Random();
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < length; i++) {
            int index = random.nextInt(CHARS.length());
            char c = CHARS.charAt(index);
            sb.append(c);
        }
        String randomString = sb.toString();
        // 使用 Redis 的 SetNX 命令确保生成的字符串不会重复
        if (Boolean.TRUE.equals(redisTemplate.opsForValue().setIfAbsent("random_string:" + randomString, "true"))) {
            return randomString;
        } else {
            return generate(length);
        }

    }
}
