server:
  port: 9402

# Spring
spring:
  application:
    # 应用名称
    name: ruoyi-test-mq
  profiles:
    # 环境配置
    active: @profiles.active@

--- # rabbitmq 配置
spring:
  rabbitmq:
    host: localhost
    port: 5672
    username: guest
    password: guest
    publisher-returns: true
    publisher-confirm-type: correlated

--- # kafka 配置
spring:
  kafka:
    bootstrap-servers: localhost:9092
    consumer:
      group-id: test-group-id # 消费者组ID
      auto-offset-reset: earliest # 当没有偏移量或偏移量无效时，从何处开始消费
    producer:
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.apache.kafka.common.serialization.StringSerializer
    # 以下配置均应该在 kafka 配置文件内编写 写到此处是为了方便调试
    properties:
      # 开启自动创建话题功能，默认是false，根据需要设置
      auto.create.topics.enable: true
      # 默认副本数 1 避免单机使用无法创建 topic
      default.replication.factor: 1

--- # rocketmq 配置
rocketmq:
  name-server: localhost:9876
  producer:
    # 生产者组
    group: dist-test

--- # nacos 配置
spring:
  cloud:
    nacos:
      # nacos 服务地址
      server-addr: @nacos.server@
      username: @nacos.username@
      password: @nacos.password@
      discovery:
        # 注册组
        group: @nacos.discovery.group@
        namespace: ${spring.profiles.active}
      config:
        # 配置组
        group: @nacos.config.group@
        namespace: ${spring.profiles.active}
  config:
    import:
      - optional:nacos:application-common.yml
