package org.dromara.acct;


import org.apache.dubbo.config.spring.context.annotation.EnableDubbo;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.context.metrics.buffering.BufferingApplicationStartup;

/**
 * <AUTHOR>
 * @Date 2025/6/17
 */
@EnableDubbo
@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class})
public class PayAcctApplication {
    public static void main(String[] args) {
        SpringApplication application = new SpringApplication(PayAcctApplication.class);
        application.setApplicationStartup(new BufferingApplicationStartup(2048));
        application.run(args);
        System.out.println("(♥◠‿◠)ﾉﾞ  pay-acct模块启动成功   ლ(´ڡ`ლ)ﾞ  ");
    }
}
