package org.dromara.acct.controller;

import lombok.RequiredArgsConstructor;
import org.apache.seata.spring.annotation.GlobalTransactional;
import org.dromara.acct.domain.bo.PayClientBo;
import org.dromara.acct.domain.vo.PayUserInfoVo;
import org.dromara.acct.service.PayClientService;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.client.ReleaseLoginLimit;
import org.dromara.common.core.validate.client.ReleaseTradeLimit;
import org.dromara.common.core.validate.client.UsdExchange;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Date;

/**
 * 客户管理
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/payClient")
public class PayClientController extends BaseController {

    private final PayClientService payClientService;

    /**
     * 获取客户列表
     */
//    @ApiEncrypt
    @PostMapping("/list")
//    @SaCheckPermission("payClient:user:list")
    public TableDataInfo<PayUserInfoVo> list(@RequestBody PayClientBo bo) {
        return payClientService.queryPageList(bo);
    }

    /**
     * 解除交易受限
     */
    @PostMapping("/releaseTradeLimit")
    @RepeatSubmit
    //    @SaCheckPermission("payClient:release:trade")
    public R<Void> releaseTradeLimit(@RequestBody @Validated(value = ReleaseTradeLimit.class) PayClientBo bo) {
        return toAjax(payClientService.updateTradeLimitExpired(bo.getUserId(), new Date()));
    }

    /**
     * 解除登录受限
     */
    @PostMapping("/releaseLoginLimit")
    @RepeatSubmit
    //    @SaCheckPermission("payClient:release:login")
    public R<Void> releaseLoginLimit(@RequestBody @Validated(value = ReleaseLoginLimit.class) PayClientBo bo) {
        return toAjax(payClientService.updateLoginLimitExpired(bo.getUserId(), new Date()));
    }

    /**
     * usd兑换 其他加密货币
     */
    @PostMapping("/usdExchangeOut")
    @RepeatSubmit
    //    @SaCheckPermission("payClient:user:usdExchangeOut")
//    @GlobalTransactional(name = "payClient-usdExchangeOut", rollbackFor = Exception.class)
    public R<Void> usdExchangeOut(@RequestBody @Validated(value = UsdExchange.class) PayClientBo bo) {
        return payClientService.usdExchangeOut(bo);
    }

}
