package org.dromara.acct.controller;

import lombok.RequiredArgsConstructor;
import org.dromara.acct.domain.bo.PayUserCommissionBo;
import org.dromara.acct.domain.vo.PayUserCommissionVo;
import org.dromara.acct.service.PayCommissionService;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 佣金管理
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/payCommission")
public class PayCommissionController {

    private final PayCommissionService payCommissionService;

    /**
     * 获取佣金列表
     */
//    @ApiEncrypt
    @PostMapping("/allTeamCommission")
    public TableDataInfo<PayUserCommissionVo> queryPageList(@RequestBody PayUserCommissionBo Bo) {
        return payCommissionService.queryCommissionPageList(Bo);
    }


}
