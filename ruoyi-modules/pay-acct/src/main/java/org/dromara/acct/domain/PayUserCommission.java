package org.dromara.acct.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@TableName("k_meta_user_commission")
public class PayUserCommission {

    /**
     * 主键id
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 用户邮箱
     */
    private String userName;

    /**
     *  佣金类型:1-开卡返佣,2-充值返佣，3-节点升级（usd）
     */
    private String commissionType;

    /**
     * 佣金费率
     */
    private BigDecimal commissionFeeRebate;

    /**
     * 佣金：单位默认usd
     */
    private BigDecimal commissionAmount;

    /**
     * 佣金描述
     */
    private String commissionDesc;

    /**
     * 交易流水ID
     */
    private Long txnId;

    /**
     * 佣金描述
     */
    private String txnCurrency;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;
}
