package org.dromara.acct.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.acct.domain.vo.PayUserInfoVo;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2025/6/19
 */
@Data
@TableName("k_meta_user_info")
@AutoMapper(target = PayUserInfoVo.class)
public class PayUserInfo {

    /**
     * 主键ID
     */
    @TableId(value = "user_id")
    private Long userId;

    /**
     * USD余额
     */
    private BigDecimal usdBalance;

    /**
     * 冻结的USD余额
     */
    private BigDecimal freezeUsdBalance;

    /**
     * 节点等级
     */
    private String nodeLevel;

    /**
     * 交易密码
     */
    @JsonIgnore
    private String tradePasswd;

    /**
     * 银卡激活码数量余额
     */
    private Integer silverActiveCodeNum;

    /**
     * 银卡已用激活码数量
     */
    private Integer silverActiveCodeUsed;

    /**
     * 金卡激活码数量余额
     */
    private Integer goldenActiveCodeNum;

    /**
     * 金卡已用激活码数量
     */
    private Integer goldenActiveCodeUsed;

    /**
     * 黑金卡激活码数量余额
     */
    private Integer goldenblackActiveCodeNum;

    /**
     * 黑金卡已用激活码数量
     */
    private Integer goldenblackActiveCodeUsed;

    /**
     * 渠道用户ID
     */
    private Long channelUserId;

    /**
     * 推荐人
     */
    private Long referrerId;


    /**
     * 交易认证方式
     */
    private String tradeAuthType;

    /**
     * moonbank用户注册的uid
     */
    private String moonbankUid;

    /**
     * 白卡激活码数量余额
     */
    private Integer whiteActiveCodeNum;

    /**
     * 白卡已用激活码数量
     */
    private Integer whiteActiveCodeUsed;

    /**
     * gate的uid
     */
    private String gateUid;

    /**
     * 自动转换USD
     */
    private String autoToUsd;
}
