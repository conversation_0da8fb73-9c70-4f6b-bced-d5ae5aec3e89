package org.dromara.acct.domain.bo;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import lombok.Data;
import org.dromara.common.core.validate.client.ReleaseLoginLimit;
import org.dromara.common.core.validate.client.ReleaseTradeLimit;
import org.dromara.common.core.validate.client.UsdExchange;
import org.dromara.common.mybatis.core.page.PageQuery;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2025/6/19
 */
@Data
public class PayClientBo extends PageQuery {

    /**
     * UID
     */
    private String nickName;

    /**
     * 客户邮箱
     */
    private String userName;

    /**
     * 状态
     */
    private String status;

    /**
     * 开始时间
     */
    private String startDate;

    /**
     * 结束时间
     */
    private String endDate;

    /**
     * 风控标志
     */
    private String limitFlag;

    /**
     * 节点等级
     */
    private String nodeLevel;

    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空", groups = { UsdExchange.class, ReleaseLoginLimit.class, ReleaseTradeLimit.class})
    private Long userId;

    /**
     * 币种
     */
    @NotBlank(message = "币种不能为空", groups = { UsdExchange.class })
    private String currency;

    /**
     * 金额
     */
    @NotNull(message = "金额不能为空", groups = { UsdExchange.class })
    @Positive(message = "金额必须大于0", groups = { UsdExchange.class })
    private BigDecimal amount;
}
