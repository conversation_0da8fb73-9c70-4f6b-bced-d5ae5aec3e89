package org.dromara.acct.domain.bo;

import lombok.Data;
import org.dromara.common.mybatis.core.page.PageQuery;

import java.util.List;

@Data
public class PayUserCommissionBo extends PageQuery {

    /**
     * 交易流水ID列表
     */
    private List<Long>  txnIds;

    /**
     * 开始时间
     */
    private String begin;

    /**
     * 结束时间
     */
    private String end;

    /**
     * 佣金类型:1-开卡返佣,2-充值返佣，3-节点升级（usd）
     */
    private String commissionType;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 邀请人的邮箱
     */
    private String name;

    /**
     * 邀请人的uid
     */
    private String nickName;

    /**
     * 被邀请人uid
     */
    private String invitedUid;

    /**
     * 被邀请人的邮箱
     */
    private String userName;

}
