package org.dromara.acct.domain.vo;

import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.acct.domain.PayUserCommission;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.Date;

@Data
@AutoMapper(target = PayUserCommission.class)
public class PayUserCommissionVo{

    /**
     * 主键id
     */
    private Long id;

    /**
     * 用户id
     */
    private Long userId;

    /**
     *  佣金类型:1-开卡返佣,2-充值返佣，3-节点升级（usd）
     */
    private String commissionType;

    /**
     * 佣金费率
     */
    private BigDecimal commissionFeeRebate;

    /**
     * 佣金：单位默认usd
     */
    private BigDecimal commissionAmount;

    /**
     * 佣金描述
     */
    private String commissionDesc;

    /**
     * 交易流水ID
     */
    private Long txnId;

    /**
     * 佣金描述
     */
    private String txnCurrency;

    /**
     * 邀请人的邮箱
     */
    private String name;

    /**
     * 邀请人的uid
     */
    private String nickName;

    /**
     * 被邀请人uid
     */
    private String invitedUid;

    /**
     * 被邀请人的邮箱
     */
    private String userName;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 开始时间
     */
    private String begin;

    /**
     * 结束时间
     */
    private String end;
}
