package org.dromara.acct.service;

import org.dromara.acct.domain.bo.PayClientBo;
import org.dromara.acct.domain.vo.PayUserInfoVo;
import org.dromara.common.core.domain.R;
import org.dromara.common.mybatis.core.page.TableDataInfo;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2025/6/19
 */

public interface PayClientService {

    /**
     * 根据条件查询客户分页数据
     * @param bo
     * @return
     */
    TableDataInfo<PayUserInfoVo> queryPageList(PayClientBo bo);

    /**
     * 解除交易受限
     * @param userId
     * @param date
     * @return
     */
    int updateTradeLimitExpired(Long userId, Date date);

    /**
     * 解除登录受限
     * @param userId
     * @param date
     * @return
     */
    int updateLoginLimitExpired(Long userId, Date date);

    /**
     * usd兑换 其他加密货币
     * @param bo
     */
    R<Void> usdExchangeOut(PayClientBo bo);
}
