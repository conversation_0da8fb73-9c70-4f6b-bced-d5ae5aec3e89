package org.dromara.acct.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.dromara.acct.domain.bo.PayUserCommissionBo;
import org.dromara.acct.domain.vo.PayUserCommissionVo;
import org.dromara.common.mybatis.core.page.PageQuery;

public interface PayUserCommissionService {

    /**
     * 根据条件分页查询用户佣金列表
     * @param userCommissionBo
     * @return
     */
    IPage<PayUserCommissionVo> queryPageByParams(PageQuery pageQuery, PayUserCommissionBo userCommissionBo);
}
