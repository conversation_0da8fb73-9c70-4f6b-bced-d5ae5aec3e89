package org.dromara.acct.service;

import org.dromara.acct.domain.bo.PayUserInfoBo;
import org.dromara.acct.domain.vo.PayUserInfoVo;

import java.util.List;

public interface PayUserInfoService {


    /**
     * 根据条件查询用户信息列表
     * @param payUserInfoBo
     * @return
     */
    List<PayUserInfoVo> queryUserInfoListByParam(PayUserInfoBo payUserInfoBo);

    /**
     * 扣减USD余额
     * @param payUserInfoBo
     * @return
     */
    boolean deductBalance(PayUserInfoBo payUserInfoBo);

    /**
     * 根据用户ID查询用户信息
     * @param userId
     * @return
     */
    PayUserInfoVo selectOneByUserId(Long userId);
}
