package org.dromara.acct.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.dromara.acct.domain.bo.PayClientBo;
import org.dromara.acct.domain.bo.PayUserInfoBo;
import org.dromara.acct.domain.vo.PayUserInfoVo;
import org.dromara.acct.service.PayClientService;
import org.dromara.acct.service.PayUserInfoService;
import org.dromara.aset.api.RemoteCoinService;
import org.dromara.aset.api.domain.bo.RemoteCoinBo;
import org.dromara.aset.api.domain.bo.RemoteCoinTxnDtlBo;
import org.dromara.aset.api.domain.bo.RemoteTxnDtlUsdBo;
import org.dromara.aset.api.domain.vo.RemoteCoinVo;
import org.dromara.common.core.constant.CacheConstants;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.enums.*;
import org.dromara.common.core.exception.BusinessException;
import org.dromara.common.core.utils.MessageUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.redis.utils.RedisUtils;
import org.dromara.system.api.RemoteDictService;
import org.dromara.system.api.RemoteUserService;
import org.dromara.system.api.domain.bo.RemoteClientBo;
import org.dromara.system.api.domain.vo.RemoteUserVo;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2025/6/19
 */
@Service
@RequiredArgsConstructor
public class PayClientServiceImpl implements PayClientService {

    private final PayUserInfoService payUserInfoService;

    @DubboReference(timeout = 10000)
    private RemoteUserService remoteUserService;
    @DubboReference
    private RemoteCoinService remoteCoinService;
    @DubboReference
    private RemoteDictService remoteDictService;

    /**
     * 根据条件查询客户分页数据
     * @param bo
     * @return
     */
    @Override
    public TableDataInfo<PayUserInfoVo> queryPageList(PayClientBo bo) {
        PageQuery pageQuery = new PageQuery(bo.getPageSize(), bo.getPageNum());

        PayUserInfoBo payUserInfoBo = new PayUserInfoBo();
        payUserInfoBo.setNodeLevel(bo.getNodeLevel());

        List<PayUserInfoVo> clientList = payUserInfoService.queryUserInfoListByParam(payUserInfoBo);

        Map<Long, PayUserInfoVo> payClinetMap = clientList.stream().collect(Collectors.toMap(PayUserInfoVo::getUserId, Function.identity()));
        List<Long> ids = new ArrayList<>(payClinetMap.keySet());

        // 查询用户信息
        RemoteClientBo remoteClientBo = new RemoteClientBo();
        BeanUtil.copyProperties(bo, remoteClientBo);
        remoteClientBo.setIds(ids);
        remoteClientBo.setPageNum(pageQuery.getPageNum());
        remoteClientBo.setPageSize(pageQuery.getPageSize());
        List<RemoteUserVo> remoteUserVos = remoteUserService.selectUserPageByParam(remoteClientBo);
        Map<Long, RemoteUserVo> userVoMap = remoteUserVos.stream()
            .collect(Collectors.toMap(RemoteUserVo::getUserId, Function.identity()));
        List<Long> userIds = new ArrayList<>(userVoMap.keySet());

        // 查询用户的钱包信息
        RemoteCoinBo remoteCoinBo = new RemoteCoinBo();
        remoteCoinBo.setUserIds(userIds);
        List<RemoteCoinVo> remoteCoinVos = remoteCoinService.selectWalletListByParam(remoteCoinBo);

        List<PayUserInfoVo> result = new ArrayList<>();

        // 整合数据返回
        for (RemoteUserVo vo : remoteUserVos) {
            // 插入基本数据
            PayUserInfoVo payUserInfoVo = payClinetMap.get(vo.getUserId());
            BeanUtil.copyProperties(vo, payUserInfoVo);
            payUserInfoVo.setNodeLevelLabel(remoteDictService.selectDictLabel(DictType.ACCT_NODE_TYPE.getValue(), payUserInfoVo.getNodeLevel()));
            if (StringUtils.isNotBlank(payUserInfoVo.getChannelUserId())){
                String channelName = payClinetMap.get(Long.parseLong(payUserInfoVo.getChannelUserId())).getUserName();
                payUserInfoVo.setChannelName(channelName);
            }
            // 插入钱包数据
            List<RemoteCoinVo> userCoins = remoteCoinVos.stream()
                .filter(coin -> coin.getUserId().equals(vo.getUserId()))
                .collect(Collectors.toList());
            payUserInfoVo.setCoinBalanceList(userCoins);
            result.add(payUserInfoVo);
        }

        Page<PayUserInfoVo> page = new Page<>();
        page.setRecords(result);
        if (!result.isEmpty()){
            page.setTotal(remoteUserVos.get(0).getTotal());
        }
        return TableDataInfo.build(page);
    }

    /**
     * 解除交易受限
     * @param userId
     * @param date
     * @return
     */
    @Override
    public int updateTradeLimitExpired(Long userId, Date date) {
        RemoteClientBo remoteClientBo = new RemoteClientBo();
        remoteClientBo.setUserId(userId);
        remoteClientBo.setTradeLimitExpired(date);
        int i = remoteUserService.updateUserInfo(remoteClientBo);
        if (i > 0) {
            RedisUtils.deleteObject(CacheConstants.TRADE_ERROR_KEY + userId);
        }
        return i;
    }

    /**
     * 解除登录受限
     * @param userId
     * @param date
     * @return
     */
    @Override
    public int updateLoginLimitExpired(Long userId, Date date) {
        RemoteClientBo remoteClientBo = new RemoteClientBo();
        remoteClientBo.setUserId(userId);
        remoteClientBo.setLoginLimitExpired(date);
        int i = remoteUserService.updateUserInfo(remoteClientBo);
        if (i > 0) {
            String userName = remoteUserService.selectUserNameById(userId);
            RedisUtils.deleteObject(CacheConstants.LOGIN_FAIL_PASS_KEY + userName);
            RedisUtils.deleteObject(CacheConstants.LOGIN_FAIL_EMAIL_KEY + userName);
            RedisUtils.deleteObject(CacheConstants.LOGIN_FAIL_GOOGLE_KEY + userName);
        }
        return i;
    }

    /**
     * usd兑换 其他加密货币
     * @param bo
     */
    @Override
    public R<Void> usdExchangeOut(PayClientBo bo) {
        List<String> Currencies = List.of(CoinType.USDC.getValue(), CoinType.USDT.getValue());

        if (!Currencies.contains(bo.getCurrency())) throw new BusinessException(MessageUtils.message("common.currency.not.supported", bo.getCurrency()));

        Long userId = bo.getUserId();
        BigDecimal amount = bo.getAmount();
        String currency = bo.getCurrency();

        // 扣减USD余额
        PayUserInfoBo  payUserInfoBo = new PayUserInfoBo();
        payUserInfoBo.setUserId(userId);
        payUserInfoBo.setAmount(amount);
        boolean b = payUserInfoService.deductBalance(payUserInfoBo);
        if (!b) {
            throw new BusinessException(MessageUtils.message("wallet.account.insufficient.balance"));
        }

        // 增加钱包余额
        RemoteCoinBo remoteCoinBo = new RemoteCoinBo();
        remoteCoinBo.setUserId(userId);
        remoteCoinBo.setAmount(amount);
        remoteCoinBo.setCurrency(currency);
        boolean coinBalance = remoteCoinService.IncreaseCoinBalance(remoteCoinBo);
        if (!coinBalance) {
            throw new BusinessException(MessageUtils.message("common.system.error"));
        }

        PayUserInfoVo userInfo = payUserInfoService.selectOneByUserId(userId);
        RemoteCoinVo wallet = remoteCoinService.selectOneByUserIdAndCurrency(remoteCoinBo);

        // 保存USD交易记录
        RemoteTxnDtlUsdBo txnDtlUSD = new RemoteTxnDtlUsdBo();
        txnDtlUSD.setUserId(userId);
        txnDtlUSD.setTxnType(TxnType.COIN_EXCHANGE_OUT.getValue());
        txnDtlUSD.setTxnAmount(amount);
        txnDtlUSD.setTxnNum(0L);
        txnDtlUSD.setTxnFee(BigDecimal.ZERO);
        txnDtlUSD.setTxnTime(LocalDateTime.now());
        txnDtlUSD.setFromUserId(userId);
        txnDtlUSD.setToUserId(Long.valueOf(TenantEnum.KAZEPAY.getValue()));
        txnDtlUSD.setTxnStatus(BusinessStatus.COIN_SUCCESS.getValue());
        txnDtlUSD.setTxnDesc(TxnType.COIN_EXCHANGE_OUT.getName());
        txnDtlUSD.setUsdBalance(userInfo.getUsdBalance());
        txnDtlUSD.setFromCoin(CoinType.USD.getValue());
        txnDtlUSD.setFromAmount(amount);
        txnDtlUSD.setExchgRate(BigDecimal.ONE);
        remoteCoinService.saveUsdTxnDtl(txnDtlUSD);

        // 保存币种交易记录
        RemoteCoinTxnDtlBo coinTxnDtl = new RemoteCoinTxnDtlBo();
        coinTxnDtl.setUserId(userId);
        coinTxnDtl.setRecordType(TxnCategory.INCOME.getValue());
        coinTxnDtl.setTxnCode(TxnType.COIN_EXCHANGE_IN.getValue());
        coinTxnDtl.setFromAddress(TenantEnum.KAZEPAY.getValue());
        coinTxnDtl.setToAddress(userId.toString());
        coinTxnDtl.setTxnCoin(currency);
        coinTxnDtl.setTxnStatus(Integer.valueOf(BusinessStatus.COIN_SUCCESS.getValue()));
        coinTxnDtl.setTxnDesc(TxnType.COIN_EXCHANGE_IN.getName());
        coinTxnDtl.setTxnAmount(amount);
        coinTxnDtl.setTxnFee(BigDecimal.ZERO);
        coinTxnDtl.setTxnTime(new Date());
        coinTxnDtl.setFromCoin(CoinType.USD.getValue());
        coinTxnDtl.setFromAmount(amount);
        coinTxnDtl.setToCoin(currency);
        coinTxnDtl.setToAmount(amount);
        coinTxnDtl.setExchgRate(BigDecimal.ONE);
        coinTxnDtl.setUserBalance(wallet.getCoinBalance());
        coinTxnDtl.setCreateTime(new Date());
        remoteCoinService.saveCoinTxnDtl(coinTxnDtl);

        return R.ok();
    }
}
