package org.dromara.acct.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.dromara.acct.domain.bo.PayUserCommissionBo;
import org.dromara.acct.domain.vo.PayUserCommissionVo;
import org.dromara.acct.service.PayCommissionService;
import org.dromara.acct.service.PayUserCommissionService;
import org.dromara.aset.api.RemoteCoinService;
import org.dromara.aset.api.domain.bo.RemoteCoinBo;
import org.dromara.aset.api.domain.vo.RemoteCoinTxnDtlVo;
import org.dromara.aset.api.domain.vo.RemoteTxnDtlUsdVo;
import org.dromara.common.core.constant.KazePayCommonConstants;
import org.dromara.common.core.enums.CoinType;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.satoken.utils.LoginHelper;
import org.dromara.system.api.RemoteUserService;
import org.dromara.system.api.domain.bo.RemoteClientBo;
import org.dromara.system.api.domain.vo.RemoteUserVo;
import org.dromara.system.api.model.LoginUser;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2025/6/26
 */
@Service
@RequiredArgsConstructor
public class PayCommissionServiceImpl implements PayCommissionService {

    private final PayUserCommissionService payUserCommissionService;

    @DubboReference(timeout = 10000)
    private RemoteUserService remoteUserService;
    @DubboReference
    private RemoteCoinService remoteCoinService;


    /**
     * 获取佣金列表
     * @param bo
     * @return
     */
    @Override
    public TableDataInfo<PayUserCommissionVo> queryCommissionPageList(PayUserCommissionBo bo) {
        PageQuery pageQuery = new PageQuery(bo.getPageSize(), bo.getPageNum());

        PayUserCommissionBo userCommissionBo = new PayUserCommissionBo();
        userCommissionBo.setCommissionType(bo.getCommissionType());
        userCommissionBo.setBegin(bo.getBegin());
        userCommissionBo.setEnd(bo.getEnd());
        userCommissionBo.setUserName(bo.getName());

        List<RemoteCoinTxnDtlVo> remoteCoinTxnDtlVos = new ArrayList<>();
        List<RemoteTxnDtlUsdVo> remoteTxnDtlUsdVos = new ArrayList<>();

        // 条件含有邀请人UID来查询 -> 通过用户的ID去查
        if (StringUtils.isNotBlank(bo.getNickName())) {
            RemoteUserVo remoteUserVo = remoteUserService.selectUserByNickName(bo.getNickName());
            userCommissionBo.setUserId(remoteUserVo.getUserId());
        }

        // 条件含有被邀请人邮箱或UID -> 先查询该用户所有的USDT和USD交易记录 -> 通过交易ID分页查询佣金列表
        if (StringUtils.isNotBlank(bo.getInvitedUid()) || StringUtils.isNotBlank(bo.getUserName())) {
            Long userId;
            List<Long> txnIds = new ArrayList<>();
            if (StringUtils.isNotBlank(bo.getUserName())){
                LoginUser userInfo = remoteUserService.getUserInfo(bo.getUserName(), LoginHelper.getTenantId());
                userId = userInfo.getUserId();
            } else {
                String nickName = StringUtils.isNotBlank(bo.getNickName()) ? bo.getNickName() : bo.getInvitedUid();
                RemoteUserVo remoteUserVo = remoteUserService.selectUserByNickName(nickName);
                userId = remoteUserVo.getUserId();
            }
            RemoteCoinBo remoteCoinBo = new RemoteCoinBo();
            remoteCoinBo.setUserId(userId);
            remoteCoinBo.setTxnTypeList(KazePayCommonConstants.PAY_REBATE_TYPE);
            remoteCoinTxnDtlVos = remoteCoinService.queryCoinTxnDtlListByParams(remoteCoinBo);
            remoteTxnDtlUsdVos = remoteCoinService.queryTxnDtlUsdListByParams(remoteCoinBo);
            remoteCoinTxnDtlVos.forEach(remoteCoinTxnDtlVo -> {
                txnIds.add(remoteCoinTxnDtlVo.getTxnId());
            });
            remoteTxnDtlUsdVos.forEach(remoteTxnDtlUsdVo -> {
               txnIds.add(remoteTxnDtlUsdVo.getId());
            });
            userCommissionBo.setTxnIds(txnIds);
        }

        // 查询佣金列表
        IPage<PayUserCommissionVo> page = payUserCommissionService.queryPageByParams(pageQuery, userCommissionBo);
        List<PayUserCommissionVo> vos = page.getRecords();
        List<Long> userIds = new ArrayList<>();

        // 若先查询分页数据 -> 查询后拿到所有TxnId再去数据库查相应的客户Id，为了组装邀请人和被邀请人的信息
        if (!vos.isEmpty() && remoteCoinTxnDtlVos.isEmpty() &&  remoteTxnDtlUsdVos.isEmpty()) {
            List<Long> USDTTxnIds  = new ArrayList<>();
            List<Long> USDTxnIds  = new ArrayList<>();
            for (PayUserCommissionVo vo : vos) {
                if (vo.getTxnCurrency().equals(CoinType.USDT.getValue())) USDTTxnIds.add(vo.getTxnId());
                if (vo.getTxnCurrency().equals(CoinType.USD.getValue())) USDTxnIds.add(vo.getTxnId());
                userIds.add(vo.getUserId());
            }
            RemoteCoinBo remoteCoinBo = new RemoteCoinBo();
            if (!USDTTxnIds.isEmpty()) {
                remoteCoinBo.setTxnIds(USDTTxnIds);
                remoteCoinTxnDtlVos = remoteCoinService.queryCoinTxnDtlListByParams(remoteCoinBo);
            }
            if (!USDTxnIds.isEmpty()) {
                remoteCoinBo.setTxnIds(USDTxnIds);
                remoteTxnDtlUsdVos = remoteCoinService.queryTxnDtlUsdListByParams(remoteCoinBo);
            }
        } else {
            for (PayUserCommissionVo vo : vos) {
                userIds.add(vo.getUserId());
            }
        }

        // 组装邀请人和被邀请人的信息
        if (!vos.isEmpty()){
            Map<Long, RemoteCoinTxnDtlVo> coinTxnMap = new HashMap<>();
            Map<Long, RemoteTxnDtlUsdVo> usdTxnMap = new HashMap<>();
            for (RemoteCoinTxnDtlVo remoteCoinTxnDtlVo : remoteCoinTxnDtlVos) {
                userIds.add(remoteCoinTxnDtlVo.getUserId());
                coinTxnMap.put(remoteCoinTxnDtlVo.getTxnId(), remoteCoinTxnDtlVo);
            }
            for (RemoteTxnDtlUsdVo remoteTxnDtlUsdVo : remoteTxnDtlUsdVos) {
                userIds.add(remoteTxnDtlUsdVo.getUserId());
                usdTxnMap.put(remoteTxnDtlUsdVo.getId(), remoteTxnDtlUsdVo);
            }
            RemoteClientBo remoteClientBo = new RemoteClientBo();
            remoteClientBo.setIds(userIds);
            List<RemoteUserVo> remoteUserVos = remoteUserService.selectUserListByParam(remoteClientBo);
            Map<Long, RemoteUserVo> userMap = remoteUserVos.stream().collect(Collectors.toMap(RemoteUserVo::getUserId, Function.identity()));
            for (PayUserCommissionVo vo : vos) {
                if (vo.getTxnCurrency().equals(CoinType.USDT.getValue())){
                    vo.setInvitedUid(userMap.get(vo.getUserId()).getNickName());
                    Long userId = coinTxnMap.get(vo.getTxnId()).getUserId();
                    vo.setName(userMap.get(userId).getUserName());
                    vo.setNickName(userMap.get(userId).getNickName());
                }
                if (vo.getTxnCurrency().equals(CoinType.USD.getValue())){
                    vo.setInvitedUid(userMap.get(vo.getUserId()).getNickName());
                    Long userId = usdTxnMap.get(vo.getTxnId()).getUserId();
                    vo.setName(userMap.get(userId).getUserName());
                    vo.setNickName(userMap.get(userId).getNickName());
                }
            }
        }

        return TableDataInfo.build(page);
    }

}
