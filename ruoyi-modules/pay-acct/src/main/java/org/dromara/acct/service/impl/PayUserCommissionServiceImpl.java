package org.dromara.acct.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.RequiredArgsConstructor;
import org.dromara.acct.domain.PayUserCommission;
import org.dromara.acct.domain.bo.PayUserCommissionBo;
import org.dromara.acct.domain.vo.PayUserCommissionVo;
import org.dromara.acct.mapper.PayUserCommissionMapper;
import org.dromara.acct.service.PayUserCommissionService;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class PayUserCommissionServiceImpl implements PayUserCommissionService {

    private final PayUserCommissionMapper payUserCommissionMapper;

    /**
     * 根据条件分页查询用户佣金列表
     * @param bo
     * @return
     */
    @Override
    public IPage<PayUserCommissionVo> queryPageByParams(PageQuery pageQuery, PayUserCommissionBo bo) {
        LambdaQueryWrapper<PayUserCommission> lqw = new LambdaQueryWrapper<>();
        lqw.eq(bo.getCommissionType() != null, PayUserCommission::getCommissionType, bo.getCommissionType());
        lqw.eq(bo.getUserId() != null, PayUserCommission::getUserId, bo.getUserId());
        lqw.eq(StringUtils.isNotBlank(bo.getUserName()), PayUserCommission::getUserName, bo.getUserName());
        lqw.in(bo.getTxnIds() != null && !bo.getTxnIds().isEmpty(), PayUserCommission::getTxnId, bo.getTxnIds());
        lqw.ge(StringUtils.isNotBlank(bo.getBegin()), PayUserCommission::getCreateTime, bo.getBegin());
        lqw.le(StringUtils.isNotBlank(bo.getEnd()), PayUserCommission::getCreateTime, bo.getEnd());
        lqw.orderByDesc(PayUserCommission::getCreateTime);
        return payUserCommissionMapper.selectVoPage(pageQuery.build(), lqw);
    }
}
