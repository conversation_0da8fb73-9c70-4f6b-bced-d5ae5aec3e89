package org.dromara.acct.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import lombok.RequiredArgsConstructor;
import org.dromara.acct.domain.PayUserInfo;
import org.dromara.acct.domain.bo.PayUserInfoBo;
import org.dromara.acct.domain.vo.PayUserInfoVo;
import org.dromara.acct.mapper.PayUserInfoMapper;
import org.dromara.acct.service.PayUserInfoService;
import org.dromara.common.core.constant.CacheConstants;
import org.dromara.common.core.enums.CoinType;
import org.dromara.common.core.utils.MessageUtils;
import org.dromara.common.core.validate.coin.FundsOperation;
import org.dromara.common.redis.utils.RedisUtils;
import org.redisson.api.RLock;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.util.List;
import java.util.concurrent.TimeUnit;

@Service
@RequiredArgsConstructor
public class PayUserInfoServiceImpl implements PayUserInfoService {

    private final PayUserInfoMapper payUserInfoMapper;

    /**
     * 根据条件查询用户信息列表
     * @param bo
     * @return
     */
    @Override
    public List<PayUserInfoVo> queryUserInfoListByParam(PayUserInfoBo bo) {
        return payUserInfoMapper.selectVoList(new LambdaQueryWrapper<PayUserInfo>()
            .eq(bo.getNodeLevel() != null, PayUserInfo::getNodeLevel, bo.getNodeLevel()));
    }

    /**
     * 扣减USD余额
     * @param bo
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deductBalance(@Validated(value = FundsOperation.class ) PayUserInfoBo bo) {
        RLock lock = RedisUtils.getClient().getLock(CacheConstants.COIN_BALANCE_LOCK_KEY + bo.getUserId() + ":" + CoinType.USD.getValue());

        boolean locked = false;
        try {
            locked = lock.tryLock(5, 5, TimeUnit.SECONDS);
            if (!locked) {
                throw new RuntimeException(MessageUtils.message("common.system.error"));
            }

            LambdaUpdateWrapper<PayUserInfo> luw = new LambdaUpdateWrapper<>();
            luw.setSql("usd_balance = usd_balance - " +  bo.getAmount());
            luw.eq(PayUserInfo::getUserId, bo.getUserId());
            luw.ge(PayUserInfo::getUsdBalance, bo.getAmount());
            return payUserInfoMapper.update(luw) > 0;

        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new RuntimeException(MessageUtils.message("common.system.error"));
        } finally {
            if (locked && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    /**
     * 根据用户ID查询用户信息
     * @param userId
     * @return
     */
    @Override
    public PayUserInfoVo selectOneByUserId(Long userId) {
        return payUserInfoMapper.selectVoById(userId);
    }
}
