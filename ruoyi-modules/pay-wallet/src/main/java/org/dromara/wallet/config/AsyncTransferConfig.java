package org.dromara.wallet.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * 异步转账配置
 *
 * <p>配置异步转账专用的线程池，确保异步转账任务的高效执行</p>
 *
 * <p>功能特性：</p>
 * <ul>
 *   <li>专用线程池：为异步转账提供独立的线程池</li>
 *   <li>合理配置：根据转账特性优化线程池参数</li>
 *   <li>监控友好：提供清晰的线程命名便于监控</li>
 *   <li>异常处理：配置合适的拒绝策略</li>
 * </ul>
 *
 * <AUTHOR>
 * @date 2025/7/25
 */
@Slf4j
@Configuration
@EnableAsync
public class AsyncTransferConfig {

    /**
     * 异步转账线程池配置
     *
     * @return 异步转账专用线程池
     */
    @Bean("transferExecutor")
    public Executor transferExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        
        // 核心线程数：根据转账并发需求设置
        executor.setCorePoolSize(5);
        
        // 最大线程数：处理突发转账请求
        executor.setMaxPoolSize(20);
        
        // 队列容量：缓冲待处理的转账任务
        executor.setQueueCapacity(100);
        
        // 线程空闲时间：60秒后回收多余线程
        executor.setKeepAliveSeconds(60);
        
        // 线程名称前缀：便于监控和调试
        executor.setThreadNamePrefix("transfer-async-");
        
        // 拒绝策略：调用者运行，确保转账任务不丢失
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        
        // 等待任务完成后再关闭线程池
        executor.setWaitForTasksToCompleteOnShutdown(true);
        
        // 等待时间：最多等待30秒
        executor.setAwaitTerminationSeconds(30);
        
        // 初始化线程池
        executor.initialize();
        
        log.info("异步转账线程池初始化完成: corePoolSize={}, maxPoolSize={}, queueCapacity={}",
            executor.getCorePoolSize(), executor.getMaxPoolSize(), executor.getQueueCapacity());
        
        return executor;
    }
}
