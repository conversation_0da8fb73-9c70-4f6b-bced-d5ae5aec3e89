package org.dromara.wallet.config;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 区块链类型枚举
 *
 * <p>统一管理所有区块链的映射配置，包括：</p>
 * <ul>
 *   <li>策略类型映射</li>
 *   <li>EVM兼容性标识</li>
 *   <li>链名称别名支持</li>
 *   <li>显示名称和原生代币</li>
 * </ul>
 *
 * <p>设计原则：</p>
 * <ul>
 *   <li>KISS - 保持简单愚蠢</li>
 *   <li>类型安全 - 编译时检查</li>
 *   <li>高性能 - O(1)查找</li>
 *   <li>集中管理 - 单一数据源</li>
 * </ul>
 *
 * <AUTHOR>
 * @date 2025-07-21
 */
@Getter
@AllArgsConstructor
public enum BlockchainType {

    /**
     * TRON网络
     */
    TRON("TRON", false, "TRON", "TRX",
        Set.of("TRON")),

    /**
     * Binance Smart Chain
     */
    BSC("EVM", true, "Binance Smart Chain", "BNB",
        Set.of("BSC", "BNB", "BINANCE")),

    /**
     * Arbitrum One (Layer 2)
     */
    ARB("EVM", true, "Arbitrum One", "ETH",
        Set.of("ARB", "ARBITRUM")),

    /**
     * Base (Layer 2)
     */
    BASE("EVM", true, "Base", "ETH",
        Set.of("BASE")),

    /**
     * Solana网络
     */
    SOLANA("SOLANA", false, "Solana", "SOL",
        Set.of("SOLANA", "SOL"));

    /**
     * 转账策略类型
     */
    private final String strategyType;

    /**
     * 是否为EVM兼容链
     */
    private final boolean evmCompatible;

    /**
     * 显示名称
     */
    private final String displayName;

    /**
     * 原生代币符号
     */
    private final String nativeTokenSymbol;

    /**
     * 支持的链名称别名
     */
    private final Set<String> aliases;

    // ==================== 静态缓存 ====================

    /**
     * 别名到区块链类型的映射缓存
     * 在类加载时构建，提供O(1)查找性能
     */
    private static final Map<String, BlockchainType> ALIAS_TO_TYPE_MAP;

    /**
     * EVM兼容链的别名集合缓存
     */
    private static final Set<String> EVM_COMPATIBLE_ALIASES;

    /**
     * 所有支持的链别名集合缓存
     */
    private static final Set<String> ALL_SUPPORTED_ALIASES;

    static {
        // 构建别名映射
        Map<String, BlockchainType> aliasMap = new HashMap<>();
        Set<String> evmAliases = new HashSet<>();
        Set<String> allAliases = new HashSet<>();

        for (BlockchainType type : values()) {
            for (String alias : type.aliases) {
                String upperAlias = alias.toUpperCase();
                aliasMap.put(upperAlias, type);
                allAliases.add(upperAlias);

                if (type.evmCompatible) {
                    evmAliases.add(upperAlias);
                }
            }
        }

        ALIAS_TO_TYPE_MAP = Collections.unmodifiableMap(aliasMap);
        EVM_COMPATIBLE_ALIASES = Collections.unmodifiableSet(evmAliases);
        ALL_SUPPORTED_ALIASES = Collections.unmodifiableSet(allAliases);
    }

    // ==================== 静态工具方法 ====================

    /**
     * 根据链名称查找区块链类型
     *
     * @param chainName 链名称（支持别名）
     * @return 区块链类型，如果不支持则返回null
     */
    public static BlockchainType fromChainName(String chainName) {
        if (chainName == null || chainName.trim().isEmpty()) {
            return null;
        }
        return ALIAS_TO_TYPE_MAP.get(chainName.toUpperCase().trim());
    }

    /**
     * 规范化链名称
     * 将输入的链名称转换为标准的主链名称
     *
     * @param chainName 输入的链名称
     * @return 规范化后的主链名称，如果不支持则返回null
     */
    public static String normalizeChainName(String chainName) {
        BlockchainType type = fromChainName(chainName);
        return type != null ? type.name() : null;
    }

    /**
     * 检查指定链是否为EVM兼容链
     *
     * @param chainName 链名称
     * @return 是否为EVM兼容链
     */
    public static boolean isEvmCompatible(String chainName) {
        if (chainName == null || chainName.trim().isEmpty()) {
            return false;
        }
        return EVM_COMPATIBLE_ALIASES.contains(chainName.toUpperCase().trim());
    }

    /**
     * 获取链对应的策略类型
     *
     * @param chainName 链名称
     * @return 策略类型，如果不支持则返回null
     */
    public static String getStrategyType(String chainName) {
        BlockchainType type = fromChainName(chainName);
        return type != null ? type.strategyType : null;
    }

    /**
     * 检查是否支持指定的链名称
     *
     * @param chainName 链名称
     * @return 是否支持
     */
    public static boolean isSupported(String chainName) {
        return fromChainName(chainName) != null;
    }

    /**
     * 获取所有支持的链名称（包括别名）
     *
     * @return 支持的链名称集合
     */
    public static Set<String> getAllSupportedChainNames() {
        return ALL_SUPPORTED_ALIASES;
    }

    /**
     * 获取所有主链名称
     *
     * @return 主链名称集合
     */
    public static Set<String> getPrimaryChainNames() {
        return Arrays.stream(values())
            .map(Enum::name)
            .collect(Collectors.toSet());
    }

    /**
     * 获取所有EVM兼容链名称
     *
     * @return EVM兼容链名称集合
     */
    public static Set<String> getEvmCompatibleChainNames() {
        return EVM_COMPATIBLE_ALIASES;
    }

    /**
     * 获取指定策略类型支持的所有链名称
     *
     * @param strategyType 策略类型
     * @return 支持的链名称集合
     */
    public static Set<String> getChainNamesByStrategyType(String strategyType) {
        if (strategyType == null) {
            return Collections.emptySet();
        }

        return Arrays.stream(values())
            .filter(type -> strategyType.equals(type.strategyType))
            .flatMap(type -> type.aliases.stream())
            .map(String::toUpperCase)
            .collect(Collectors.toSet());
    }

    /**
     * 获取配置摘要信息
     *
     * @return 配置摘要字符串
     */
    public static String getConfigSummary() {
        StringBuilder summary = new StringBuilder();
        summary.append("区块链映射配置摘要:\n");

        for (BlockchainType type : values()) {
            summary.append(String.format("  %s: 别名=%s, EVM兼容=%s, 策略=%s\n",
                type.name(), type.aliases, type.evmCompatible, type.strategyType));
        }

        return summary.toString();
    }
}
