package org.dromara.wallet.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 私钥加密配置属性类
 * 从yml配置文件中读取加密相关配置
 *
 * <AUTHOR>
 * @date 2025/1/27
 */
@Data
@Component
@ConfigurationProperties(prefix = "wallet-encryption")
public class WalletPrivateKeyProperties {

    /**
     * 是否启用加密
     */
    private boolean enabled = true;

    /**
     * 加密密钥
     */
    private String secretKey = "MetaWallet123456";

    /**
     * 加密算法
     */
    private String algorithm = "AES";

    /**
     * 编码方式
     */
    private String encoding = "BASE64";

    @Override
    public String toString() {
        return "EncryptionProperties{" +
            "enabled=" + enabled +
            ", algorithm='" + algorithm + '\'' +
            ", encoding='" + encoding + '\'' +
            ", secretKeyLength=" + (secretKey != null ? secretKey.length() : 0) +
            '}';
    }
}

