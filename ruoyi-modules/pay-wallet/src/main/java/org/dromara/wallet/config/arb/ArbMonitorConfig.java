package org.dromara.wallet.config.arb;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * ARB监控配置
 * 扁平化配置 - 监控和告警相关配置
 *
 * <AUTHOR>
 * @date 2025-06-27
 */
@Setter
@Getter
@Configuration
@ConfigurationProperties(prefix = "arb.monitor")
public class ArbMonitorConfig {

    /**
     * 是否启用监控
     */
    private boolean enabled = false;

    // ============ 交易监控配置 ============

    /**
     * 是否启用交易监控
     */
    private boolean transactionMonitorEnabled = true;

    /**
     * 交易确认数
     */
    private int confirmationBlocks = 12;

    /**
     * 交易超时时间（秒）
     */
    private int transactionTimeout = 300; // 5分钟

    /**
     * 交易重试次数
     */
    private int transactionRetryCount = 3;

    /**
     * 交易监控间隔（秒）
     */
    private int transactionMonitorInterval = 30;

    // ============ 余额监控配置 ============

    /**
     * 是否启用余额监控
     */
    private boolean balanceMonitorEnabled = true;

    /**
     * 余额检查间隔（秒）
     */
    private int balanceCheckInterval = 300; // 5分钟

    /**
     * 低余额阈值（ETH）
     */
    private double lowBalanceThreshold = 0.01;

    /**
     * 余额告警阈值（ETH）
     */
    private double balanceAlertThreshold = 0.005;

    // ============ 自动扫描配置 ============

    /**
     * 是否启用自动扫描功能
     * 启用后，应用启动时会自动开始ARB链的区块扫描任务
     */
    private boolean autoScanEnabled = true;

    /**
     * 自动扫描的起始区块号
     * 如果为0或负数，则从Redis中恢复上次扫描进度
     * 如果Redis中没有进度记录，则从最新区块开始扫描
     */
    private long autoScanStartBlock = 0L;

    /**
     * 自动扫描的扫描周期（毫秒）
     * ARB出块时间约0.25秒，建议设置为15秒
     */
    private long autoScanPeriod = 15000L;

    // ============ Gas费用监控配置 ============

    /**
     * 是否启用Gas费用监控
     */
    private boolean gasFeeMonitorEnabled = false;

    /**
     * Gas价格告警阈值（gwei）
     */
    private double gasPriceAlertThreshold = 2.0;

    /**
     * Gas费用监控间隔（秒）
     */
    private int gasFeeMonitorInterval = 60;

    // ============ 网络监控配置 ============

    /**
     * 是否启用网络监控
     */
    private boolean networkMonitorEnabled = true;

    /**
     * 网络健康检查间隔（秒）
     */
    private int networkHealthCheckInterval = 120; // 2分钟

    /**
     * 网络延迟告警阈值（毫秒）
     */
    private int networkLatencyThreshold = 5000; // 5秒

    /**
     * 网络错误率告警阈值（百分比）
     */
    private double networkErrorRateThreshold = 10.0; // 10%

    // ============ 告警配置 ============

    /**
     * 是否启用告警
     */
    private boolean alertEnabled = false;

    /**
     * 告警发送间隔（秒）
     */
    private int alertInterval = 300; // 5分钟

    /**
     * 告警重试次数
     */
    private int alertRetryCount = 3;

    /**
     * 告警级别：INFO, WARN, ERROR, CRITICAL
     */
    private String alertLevel = "WARN";

    // ============ 补偿配置 ============

    /**
     * 是否启用补偿功能
     */
    private boolean compensationEnabled = false;

    /**
     * 补偿检查间隔（秒）
     */
    private int compensationCheckInterval = 600; // 10分钟

    /**
     * 补偿重试次数
     */
    private int compensationRetryCount = 5;

    /**
     * 补偿超时时间（秒）
     */
    private int compensationTimeout = 1800; // 30分钟

    // ============ 性能监控配置 ============

    /**
     * 是否启用性能监控
     */
    private boolean performanceMonitorEnabled = true;

    /**
     * 性能数据收集间隔（秒）
     */
    private int performanceDataInterval = 60;

    /**
     * API响应时间阈值（毫秒）
     */
    private int apiResponseTimeThreshold = 3000; // 3秒

    /**
     * 内存使用率告警阈值（百分比）
     */
    private double memoryUsageThreshold = 80.0; // 80%

    // ============ Layer 2特有监控配置 ============

    /**
     * 是否启用Layer 2特有监控
     */
    private boolean layer2MonitorEnabled = true;

    /**
     * 排序器状态监控间隔（秒）
     */
    private int sequencerStatusInterval = 120; // 2分钟

    /**
     * 批次提交监控间隔（秒）
     */
    private int batchSubmissionInterval = 300; // 5分钟

    /**
     * 挑战期监控间隔（秒）
     */
    private int challengePeriodInterval = 3600; // 1小时

    /**
     * 获取监控配置摘要
     */
    public String getMonitorConfigSummary() {
        return String.format(
            "ARB Monitor - Enabled: %s, Transaction: %s, Balance: %s, Gas: %s, Network: %s, Alert: %s",
            enabled,
            transactionMonitorEnabled,
            balanceMonitorEnabled,
            gasFeeMonitorEnabled,
            networkMonitorEnabled,
            alertEnabled
        );
    }

    /**
     * 验证配置
     */
    public void validate() {
        if (enabled) {
            if (confirmationBlocks < 0) {
                throw new IllegalArgumentException("ARB confirmation blocks cannot be negative");
            }
            if (transactionTimeout <= 0) {
                throw new IllegalArgumentException("ARB transaction timeout must be positive");
            }
            if (transactionRetryCount < 0) {
                throw new IllegalArgumentException("ARB transaction retry count cannot be negative");
            }
            if (transactionMonitorInterval <= 0) {
                throw new IllegalArgumentException("ARB transaction monitor interval must be positive");
            }
            if (balanceCheckInterval <= 0) {
                throw new IllegalArgumentException("ARB balance check interval must be positive");
            }
            if (lowBalanceThreshold < 0) {
                throw new IllegalArgumentException("ARB low balance threshold cannot be negative");
            }
            if (balanceAlertThreshold < 0) {
                throw new IllegalArgumentException("ARB balance alert threshold cannot be negative");
            }
            if (gasPriceAlertThreshold < 0) {
                throw new IllegalArgumentException("ARB gas price alert threshold cannot be negative");
            }
            if (gasFeeMonitorInterval <= 0) {
                throw new IllegalArgumentException("ARB gas fee monitor interval must be positive");
            }
            if (networkHealthCheckInterval <= 0) {
                throw new IllegalArgumentException("ARB network health check interval must be positive");
            }
            if (networkLatencyThreshold <= 0) {
                throw new IllegalArgumentException("ARB network latency threshold must be positive");
            }
            if (networkErrorRateThreshold < 0 || networkErrorRateThreshold > 100) {
                throw new IllegalArgumentException("ARB network error rate threshold must be between 0 and 100");
            }
            if (alertInterval <= 0) {
                throw new IllegalArgumentException("ARB alert interval must be positive");
            }
            if (alertRetryCount < 0) {
                throw new IllegalArgumentException("ARB alert retry count cannot be negative");
            }
            if (compensationCheckInterval <= 0) {
                throw new IllegalArgumentException("ARB compensation check interval must be positive");
            }
            if (compensationRetryCount < 0) {
                throw new IllegalArgumentException("ARB compensation retry count cannot be negative");
            }
            if (compensationTimeout <= 0) {
                throw new IllegalArgumentException("ARB compensation timeout must be positive");
            }
            if (performanceDataInterval <= 0) {
                throw new IllegalArgumentException("ARB performance data interval must be positive");
            }
            if (apiResponseTimeThreshold <= 0) {
                throw new IllegalArgumentException("ARB API response time threshold must be positive");
            }
            if (memoryUsageThreshold < 0 || memoryUsageThreshold > 100) {
                throw new IllegalArgumentException("ARB memory usage threshold must be between 0 and 100");
            }
            if (sequencerStatusInterval <= 0) {
                throw new IllegalArgumentException("ARB sequencer status interval must be positive");
            }
            if (batchSubmissionInterval <= 0) {
                throw new IllegalArgumentException("ARB batch submission interval must be positive");
            }
            if (challengePeriodInterval <= 0) {
                throw new IllegalArgumentException("ARB challenge period interval must be positive");
            }
        }
    }

    /**
     * 是否启用任何监控功能
     */
    public boolean isAnyMonitorEnabled() {
        return enabled && (transactionMonitorEnabled || balanceMonitorEnabled ||
                          gasFeeMonitorEnabled || networkMonitorEnabled ||
                          performanceMonitorEnabled || layer2MonitorEnabled);
    }

    /**
     * 获取启用的监控功能数量
     */
    public int getEnabledMonitorCount() {
        if (!enabled) {
            return 0;
        }

        int count = 0;
        if (transactionMonitorEnabled) count++;
        if (balanceMonitorEnabled) count++;
        if (gasFeeMonitorEnabled) count++;
        if (networkMonitorEnabled) count++;
        if (performanceMonitorEnabled) count++;
        if (layer2MonitorEnabled) count++;

        return count;
    }

    /**
     * 获取告警级别枚举值
     */
    public AlertLevel getAlertLevelEnum() {
        try {
            return AlertLevel.valueOf(alertLevel.toUpperCase());
        } catch (Exception e) {
            return AlertLevel.WARN;
        }
    }

    /**
     * 告警级别枚举
     */
    public enum AlertLevel {
        INFO, WARN, ERROR, CRITICAL
    }
}
