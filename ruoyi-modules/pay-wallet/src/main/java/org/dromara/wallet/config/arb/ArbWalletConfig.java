package org.dromara.wallet.config.arb;

import lombok.Getter;
import lombok.Setter;
import org.dromara.common.tenant.helper.TenantHelper;
import org.dromara.wallet.domain.dto.MetaMainAddress;
import org.dromara.wallet.wallet.exception.WalletException;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.List;
import java.util.Objects;

/**
 * ARB钱包配置
 * 扁平化配置 - 钱包相关配置
 *
 * <AUTHOR>
 * @date 2025-06-27
 */
@Setter
@Getter
@Configuration
@ConfigurationProperties(prefix = "arb.wallet")
public class ArbWalletConfig {

    /**
     * 是否启用ARB钱包功能
     */
    private boolean enabled = true;



    /**
     * 主钱包地址列表
     */
    private List<MetaMainAddress> mainAddressList;

    /**
     * 手续费钱包配置
     */
    private FeeWalletConfig feeWallet;

    /**
     * 通过指定tenantId获取对应主钱包
     *
     * @param tenantId 租户ID
     * @return 主钱包地址，如果不存在则抛出异常
     */
    public MetaMainAddress getMainAddress(String tenantId) {
        if (mainAddressList == null || mainAddressList.isEmpty()) {
            throw new WalletException("ARB主钱包地址列表为空");
        }

        for (MetaMainAddress item : mainAddressList) {
            if (Objects.equals(tenantId, item.getTenantId())) {
                return item;
            }
        }
        throw new WalletException("租户tenantId:" + tenantId + ",对应ARB主钱包地址不存在");
    }

    /**
     * 通过当前租户获取对应主钱包
     *
     * @return 主钱包地址，如果不存在则抛出异常
     */
    public MetaMainAddress getMainAddress() {
        return getMainAddress(TenantHelper.getTenantId());
    }

    /**
     * 手续费钱包配置
     */
    @Getter
    @Setter
    public static class FeeWalletConfig {
        /**
         * 是否启用手续费钱包
         */
        private boolean enabled = true;

        /**
         * 手续费钱包私钥
         */
        private String privateKey;

        /**
         * 手续费钱包地址
         */
        private String address;

        // ============ 统一费用控制配置 ============

        /**
         * 最大Gas价格（单位：wei）
         * Layer 2网络费用更便宜
         */
        private long maxGasPrice = 1_000_000_000L; // 1 gwei

        /**
         * 最大Gas限制
         * 防止复杂合约调用消耗过多gas
         */
        private long maxGasLimit = 8_000_000L; // 8M gas

        /**
         * 验证配置
         */
        public void validate() {
            if (enabled) {
                if (privateKey == null || privateKey.trim().isEmpty()) {
                    throw new IllegalArgumentException("ARB fee wallet private key cannot be empty when enabled");
                }
                if (address == null || address.trim().isEmpty()) {
                    throw new IllegalArgumentException("ARB fee wallet address cannot be empty when enabled");
                }
                // 验证gas配置
                if (maxGasPrice <= 0) {
                    throw new IllegalArgumentException("ARB fee wallet max gas price must be positive");
                }
                if (maxGasLimit <= 0) {
                    throw new IllegalArgumentException("ARB fee wallet max gas limit must be positive");
                }
                if (maxGasLimit < 21000) {
                    throw new IllegalArgumentException("ARB fee wallet max gas limit must be at least 21000 (basic transfer)");
                }
            }
        }
    }

    /**
     * 验证主钱包配置
     */
    private void validateMainAddresses() {
        if (mainAddressList != null) {
            for (MetaMainAddress address : mainAddressList) {
                if (address.getTenantId() == null || address.getTenantId().trim().isEmpty()) {
                    throw new IllegalArgumentException("ARB main wallet sysId cannot be empty");
                }
                if (address.getAddress() == null || address.getAddress().trim().isEmpty()) {
                    throw new IllegalArgumentException("ARB main wallet address cannot be empty");
                }
                // 验证地址格式
                if (!isValidArbAddress(address.getAddress())) {
                    throw new IllegalArgumentException("Invalid ARB main wallet address format: " + address.getAddress());
                }
            }
        }
    }

    /**
     * 验证配置
     */
    public void validate() {
        if (enabled) {

            // 验证主钱包配置
            validateMainAddresses();

            // 验证手续费钱包配置
            if (feeWallet != null) {
                feeWallet.validate();
                // 验证手续费钱包地址格式
                if (feeWallet.getAddress() != null && !isValidArbAddress(feeWallet.getAddress())) {
                    throw new IllegalArgumentException("Invalid ARB fee wallet address format: " + feeWallet.getAddress());
                }
            }
        }
    }

    /**
     * 验证ARB地址格式
     */
    private boolean isValidArbAddress(String address) {
        if (address == null || address.trim().isEmpty()) {
            return false;
        }
        // ARB地址以0x开头，长度为42个字符（与以太坊相同）
        return address.matches("^0x[a-fA-F0-9]{40}$");
    }

    /**
     * 是否启用手续费钱包
     */
    public boolean isFeeWalletEnabled() {
        return enabled && feeWallet != null && feeWallet.isEnabled();
    }

    /**
     * 获取手续费钱包私钥
     */
    public String getFeeWalletPrivateKey() {
        return isFeeWalletEnabled() ? feeWallet.getPrivateKey() : null;
    }

    /**
     * 获取手续费钱包地址
     */
    public String getFeeWalletAddress() {
        return isFeeWalletEnabled() ? feeWallet.getAddress() : null;
    }

    // ============ Gas配置访问方法 ============

    /**
     * 获取最大Gas价格
     */
    public long getMaxGasPrice() {
        return feeWallet != null ? feeWallet.getMaxGasPrice() : 1_000_000_000L; // 默认1 gwei
    }

    /**
     * 获取最大Gas限制
     */
    public long getMaxGasLimit() {
        return feeWallet != null ? feeWallet.getMaxGasLimit() : 8_000_000L; // 默认8M gas
    }
}
