package org.dromara.wallet.config.base;

import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.List;
import java.util.concurrent.ThreadLocalRandom;

/**
 * BASE RPC配置
 * 扁平化配置 - RPC端点相关配置
 *
 * <AUTHOR>
 * @date 2025-06-27
 */
@Slf4j
@Setter
@Getter
@Configuration
@ConfigurationProperties(prefix = "base.rpc")
public class BaseRpcConfig {

    /**
     * 是否启用
     */
    private boolean enabled = true;

    /**
     * 网络类型：MAINNET/TESTNET
     */
    private String networkType = "TESTNET";

    /**
     * 链ID
     */
    private long chainId = 84532L; // 默认测试网

    /**
     * RPC端点（简化配置，只需要一个端点）
     */
    private String endpoint = "https://goerli.base.org";

    /**
     * 连接超时时间（秒）
     */
    private int connectionTimeout = 30;

    /**
     * 读取超时时间（秒）
     */
    private int readTimeout = 60;

    /**
     * 最大重试次数
     */
    private int maxRetries = 3;

    /**
     * 重试间隔（秒）
     */
    private int retryInterval = 2;

    /**
     * 速率限制（每秒请求数）
     */
    private int rateLimit = 100;

    /**
     * 最大并发请求数
     */
    private int maxConcurrentRequests = 50;

    /**
     * 是否启用SSL验证
     */
    private boolean sslVerificationEnabled = true;

    /**
     * 是否启用请求日志
     */
    private boolean requestLoggingEnabled = false;

    /**
     * 获取可用的RPC端点（简化版本）
     */
    public String getAvailableEndpoint() {
        return endpoint;
    }

    /**
     * 获取主要端点（兼容性方法）
     */
    public String getPrimaryEndpoint() {
        return endpoint;
    }



    /**
     * 是否为主网
     */
    public boolean isMainnet() {
        return "MAINNET".equalsIgnoreCase(networkType) || chainId == 8453L;
    }

    /**
     * 是否为测试网
     */
    public boolean isTestnet() {
        return "TESTNET".equalsIgnoreCase(networkType) || chainId == 84532L;
    }

    /**
     * 获取网络名称
     */
    public String getNetworkName() {
        if (isMainnet()) {
            return "Base Mainnet";
        } else if (isTestnet()) {
            return "Base Goerli";
        } else {
            return "Unknown BASE Network";
        }
    }

    /**
     * 验证配置
     */
    public void validate() {
        if (enabled) {

            if (chainId <= 0) {
                throw new IllegalArgumentException("BASE chain ID must be positive");
            }

            if (connectionTimeout <= 0) {
                throw new IllegalArgumentException("BASE connection timeout must be positive");
            }

            if (readTimeout <= 0) {
                throw new IllegalArgumentException("BASE read timeout must be positive");
            }

            if (maxRetries < 0) {
                throw new IllegalArgumentException("BASE max retries cannot be negative");
            }

            if (rateLimit <= 0) {
                throw new IllegalArgumentException("BASE rate limit must be positive");
            }

            // 验证端点URL格式
            if (!isValidUrl(endpoint)) {
                throw new IllegalArgumentException("Invalid BASE endpoint URL: " + endpoint);
            }
        }
    }

    /**
     * 验证URL格式
     */
    private boolean isValidUrl(String url) {
        if (url == null || url.trim().isEmpty()) {
            return false;
        }
        return url.startsWith("http://") || url.startsWith("https://") || url.startsWith("wss://");
    }

    /**
     * 获取超时配置信息
     */
    public String getTimeoutInfo() {
        return String.format("Connection: %ds, Read: %ds, MaxRetries: %d",
                           connectionTimeout, readTimeout, maxRetries);
    }
}
