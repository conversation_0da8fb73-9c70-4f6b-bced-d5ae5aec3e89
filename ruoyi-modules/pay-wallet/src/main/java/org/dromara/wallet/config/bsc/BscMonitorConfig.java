package org.dromara.wallet.config.bsc;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * BSC监控配置
 * 扁平化配置 - 监控和告警相关配置
 *
 * <AUTHOR>
 * @date 2025-06-27
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "bsc.monitor")
public class BscMonitorConfig {

    /**
     * 是否启用监控
     */
    private boolean enabled = true;

    // ============ 连接监控配置 ============

    /**
     * WebSocket连接超时时间（秒）
     */
    private int connectionTimeout = 30;

    /**
     * 最大重连次数
     */
    private int maxReconnectAttempts = 5;

    /**
     * 重连间隔（秒）
     */
    private int reconnectInterval = 10;

    /**
     * 是否启用自动重连
     */
    private boolean enableAutoReconnect = true;

    /**
     * 连接健康检查间隔（秒）
     */
    private int healthCheckInterval = 60;

    // ============ 交易监控配置 ============

    /**
     * 是否启用交易监控
     */
    private boolean transactionMonitorEnabled = true;

    /**
     * 交易确认超时时间（秒）
     */
    private int transactionTimeout = 300; // 5分钟

    /**
     * 交易状态检查间隔（秒）
     */
    private int transactionCheckInterval = 15; // BSC出块时间约3秒

    /**
     * 最大交易确认等待时间（秒）
     */
    private int maxConfirmationWaitTime = 600; // 10分钟

    /**
     * 交易失败重试次数
     */
    private int transactionRetryCount = 3;

    /**
     * 所需确认数
     */
    private int requiredConfirmations = 12; // BSC推荐确认数

    // ============ 余额监控配置 ============

    /**
     * 是否启用余额监控
     */
    private boolean balanceMonitorEnabled = true;

    /**
     * 余额检查间隔（秒）
     */
    private int balanceCheckInterval = 300; // 5分钟

    /**
     * BNB余额低阈值
     */
    private double lowBnbBalanceThreshold = 0.1;

    // ============ 自动扫描配置 ============

    /**
     * 是否启用自动扫描功能
     * 启用后，应用启动时会自动开始BSC链的区块扫描任务
     */
    private boolean autoScanEnabled = true;

    /**
     * 自动扫描的起始区块号
     * 如果为0或负数，则从Redis中恢复上次扫描进度
     * 如果Redis中没有进度记录，则从最新区块开始扫描
     */
    private long autoScanStartBlock = 0L;

    /**
     * 自动扫描的扫描周期（毫秒）
     * BSC出块时间约3秒，建议设置为15秒
     */
    private long autoScanPeriod = 15000L;

    /**
     * 代币余额低阈值（按代币符号配置）
     */
    private double lowTokenBalanceThreshold = 100.0;

    // ============ Gas费用监控配置 ============

    /**
     * 是否启用Gas费用监控
     */
    private boolean gasFeeMonitorEnabled = true;

    /**
     * Gas价格检查间隔（秒）
     */
    private int gasPriceCheckInterval = 60;

    /**
     * 高Gas价格告警阈值（gwei）
     */
    private double highGasPriceThreshold = 20.0;

    /**
     * 极高Gas价格告警阈值（gwei）
     */
    private double extremeGasPriceThreshold = 50.0;

    // ============ 网络监控配置 ============

    /**
     * 是否启用网络监控
     */
    private boolean networkMonitorEnabled = true;

    /**
     * 网络延迟检查间隔（秒）
     */
    private int networkLatencyCheckInterval = 120;

    /**
     * 网络延迟告警阈值（毫秒）
     */
    private long networkLatencyThreshold = 5000; // 5秒

    /**
     * 区块高度检查间隔（秒）
     */
    private int blockHeightCheckInterval = 60;

    /**
     * 区块高度滞后告警阈值
     */
    private int blockHeightLagThreshold = 10;

    // ============ 告警配置 ============

    /**
     * 是否启用告警
     */
    private boolean alertEnabled = true;

    /**
     * 告警发送间隔（秒）- 防止重复告警
     */
    private int alertInterval = 3600; // 1小时

    /**
     * 是否启用邮件告警
     */
    private boolean emailAlertEnabled = false;

    /**
     * 是否启用短信告警
     */
    private boolean smsAlertEnabled = false;

    // ============ 性能监控配置 ============

    /**
     * 是否启用性能监控
     */
    private boolean performanceMonitorEnabled = true;

    /**
     * API响应时间阈值（毫秒）
     */
    private long apiResponseTimeThreshold = 3000; // 3秒

    /**
     * 错误率阈值（百分比）
     */
    private double errorRateThreshold = 5.0; // 5%

    /**
     * 性能统计窗口时间（秒）
     */
    private int performanceWindowTime = 300; // 5分钟

    /**
     * TPS（每秒交易数）监控阈值
     */
    private double tpsThreshold = 100.0;

    // ============ 日志配置 ============

    /**
     * 是否启用详细日志
     */
    private boolean verboseLoggingEnabled = false;

    /**
     * 是否记录API请求日志
     */
    private boolean apiRequestLoggingEnabled = false;

    /**
     * 是否记录交易详情日志
     */
    private boolean transactionLoggingEnabled = true;

    /**
     * 日志保留天数
     */
    private int logRetentionDays = 30;

    // ============ 补偿任务配置 ============

    /**
     * 是否启用补偿任务
     */
    private boolean compensationEnabled = true;

    /**
     * 补偿任务执行间隔（cron表达式）
     */
    private String compensationCron = "0 0 */1 * * *"; // 每1小时执行一次

    /**
     * 补偿任务检查的交易数量限制
     */
    private int compensationCheckLimit = 20;

    /**
     * 补偿任务超时时间（秒）
     */
    private int compensationTimeout = 1800; // 30分钟

    // ============ MEV保护配置 ============

    /**
     * 是否启用MEV保护监控
     */
    private boolean mevProtectionEnabled = false;

    /**
     * MEV检测阈值（价格滑点百分比）
     */
    private double mevDetectionThreshold = 2.0; // 2%

    /**
     * 是否启用私有内存池
     */
    private boolean privateMempoolEnabled = false;

    // ============ 验证方法 ============

    /**
     * 验证监控配置
     */
    public void validate() {
        if (enabled) {
            if (connectionTimeout <= 0) {
                throw new IllegalArgumentException("Connection timeout must be positive");
            }
            if (maxReconnectAttempts < 0) {
                throw new IllegalArgumentException("Max reconnect attempts cannot be negative");
            }
            if (reconnectInterval <= 0) {
                throw new IllegalArgumentException("Reconnect interval must be positive");
            }
            if (healthCheckInterval <= 0) {
                throw new IllegalArgumentException("Health check interval must be positive");
            }
            if (transactionTimeout <= 0) {
                throw new IllegalArgumentException("Transaction timeout must be positive");
            }
            if (transactionCheckInterval <= 0) {
                throw new IllegalArgumentException("Transaction check interval must be positive");
            }
            if (maxConfirmationWaitTime <= 0) {
                throw new IllegalArgumentException("Max confirmation wait time must be positive");
            }
            if (transactionRetryCount < 0) {
                throw new IllegalArgumentException("Transaction retry count cannot be negative");
            }
            if (requiredConfirmations <= 0) {
                throw new IllegalArgumentException("Required confirmations must be positive");
            }
            if (balanceCheckInterval <= 0) {
                throw new IllegalArgumentException("Balance check interval must be positive");
            }
            if (lowBnbBalanceThreshold < 0) {
                throw new IllegalArgumentException("Low BNB balance threshold cannot be negative");
            }
            if (lowTokenBalanceThreshold < 0) {
                throw new IllegalArgumentException("Low token balance threshold cannot be negative");
            }
            if (gasPriceCheckInterval <= 0) {
                throw new IllegalArgumentException("Gas price check interval must be positive");
            }
            if (highGasPriceThreshold <= 0) {
                throw new IllegalArgumentException("High gas price threshold must be positive");
            }
            if (extremeGasPriceThreshold <= 0) {
                throw new IllegalArgumentException("Extreme gas price threshold must be positive");
            }
            if (highGasPriceThreshold >= extremeGasPriceThreshold) {
                throw new IllegalArgumentException("High gas price threshold must be less than extreme gas price threshold");
            }
            if (networkLatencyCheckInterval <= 0) {
                throw new IllegalArgumentException("Network latency check interval must be positive");
            }
            if (networkLatencyThreshold <= 0) {
                throw new IllegalArgumentException("Network latency threshold must be positive");
            }
            if (blockHeightCheckInterval <= 0) {
                throw new IllegalArgumentException("Block height check interval must be positive");
            }
            if (blockHeightLagThreshold <= 0) {
                throw new IllegalArgumentException("Block height lag threshold must be positive");
            }
            if (alertInterval <= 0) {
                throw new IllegalArgumentException("Alert interval must be positive");
            }
            if (apiResponseTimeThreshold <= 0) {
                throw new IllegalArgumentException("API response time threshold must be positive");
            }
            if (errorRateThreshold < 0 || errorRateThreshold > 100) {
                throw new IllegalArgumentException("Error rate threshold must be between 0 and 100");
            }
            if (performanceWindowTime <= 0) {
                throw new IllegalArgumentException("Performance window time must be positive");
            }
            if (tpsThreshold <= 0) {
                throw new IllegalArgumentException("TPS threshold must be positive");
            }
            if (logRetentionDays <= 0) {
                throw new IllegalArgumentException("Log retention days must be positive");
            }
            if (compensationCheckLimit <= 0) {
                throw new IllegalArgumentException("Compensation check limit must be positive");
            }
            if (compensationTimeout <= 0) {
                throw new IllegalArgumentException("Compensation timeout must be positive");
            }
            if (mevDetectionThreshold < 0 || mevDetectionThreshold > 100) {
                throw new IllegalArgumentException("MEV detection threshold must be between 0 and 100");
            }

        }
    }
}
