package org.dromara.wallet.config.bsc;

import lombok.Getter;
import lombok.Setter;
import org.dromara.common.tenant.helper.TenantHelper;
import org.dromara.wallet.domain.dto.MetaMainAddress;
import org.dromara.wallet.wallet.exception.WalletException;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.List;
import java.util.Objects;

/**
 * BSC钱包配置
 * 扁平化配置 - 钱包相关配置
 *
 * <AUTHOR>
 * @date 2025-06-27
 */
@Setter
@Getter
@Configuration
@ConfigurationProperties(prefix = "bsc.wallet")
public class BscWalletConfig {

    /**
     * 是否启用BSC钱包功能
     */
    private boolean enabled = true;


    /**
     * 主钱包地址列表
     */
    private List<MetaMainAddress> mainAddressList;

    /**
     * 手续费钱包配置
     */
    private FeeWalletConfig feeWallet;

    /**
     * 通过指定tenantId获取对应主钱包（归集钱包）
     *
     * @param tenantId 租户ID
     * @return 主钱包地址(归集钱包地址)，如果不存在则抛出异常
     */
    public MetaMainAddress getMainAddress(String tenantId) {
        if (mainAddressList == null || mainAddressList.isEmpty()) {
            throw new WalletException("BSC主钱包地址列表为空");
        }

        for (MetaMainAddress item : mainAddressList) {
            if (Objects.equals(tenantId, item.getTenantId())) {
                return item;
            }
        }
        throw new WalletException("租户tenantId:" + tenantId + ",对应BSC主钱包地址不存在");
    }

    /**
     * 通过当前租户获取对应主钱包（归集钱包）
     *
     * @return 主钱包地址(归集钱包地址)，如果不存在则抛出异常
     */
    public MetaMainAddress getMainAddress() {
        return getMainAddress(TenantHelper.getTenantId());
    }

    /**
     * 手续费钱包配置
     */
    @Getter
    @Setter
    public static class FeeWalletConfig {
        /**
         * 是否启用手续费钱包
         */
        private boolean enabled = true;

        /**
         * 手续费钱包私钥
         */
        private String privateKey;

        /**
         * 手续费钱包地址
         */
        private String address;

        // ============ 统一费用控制配置 ============

        /**
         * 最大Gas价格（单位：wei）
         * 防止网络拥堵时产生过高费用
         */
        private long maxGasPrice = 20_000_000_000L; // 20 gwei

        /**
         * 最大Gas限制
         * 防止复杂合约调用消耗过多gas
         */
        private long maxGasLimit = 8_000_000L; // 8M gas

        /**
         * 验证手续费钱包配置
         */
        public void validate() {
            if (enabled) {
                if (privateKey == null || privateKey.trim().isEmpty()) {
                    throw new IllegalArgumentException("Fee wallet private key is required when fee wallet is enabled");
                }
                if (address == null || address.trim().isEmpty()) {
                    throw new IllegalArgumentException("Fee wallet address is required when fee wallet is enabled");
                }

                // 验证gas配置
                if (maxGasPrice <= 0) {
                    throw new IllegalArgumentException("BSC fee wallet max gas price must be positive");
                }
                if (maxGasLimit <= 0) {
                    throw new IllegalArgumentException("BSC fee wallet max gas limit must be positive");
                }
                if (maxGasLimit < 21000) {
                    throw new IllegalArgumentException("BSC fee wallet max gas limit must be at least 21000 (basic transfer)");
                }
            }
        }
    }

    /**
     * 验证钱包配置
     */
    public void validate() {
        if (enabled) {
//            if (mainAddressList == null || mainAddressList.isEmpty()) {
//                throw new IllegalArgumentException("Main address list cannot be empty when BSC wallet is enabled");
//            }

//            // 验证主钱包地址
//            for (MetaMainAddress address : mainAddressList) {
//                if (address.getSysId() == null || address.getSysId().trim().isEmpty()) {
//                    throw new IllegalArgumentException("Main address sysId cannot be empty");
//                }
//                if (address.getAddress() == null || address.getAddress().trim().isEmpty()) {
//                    throw new IllegalArgumentException("Main address cannot be empty");
//                }
//                // 验证BSC地址格式
//                if (!isValidBscAddress(address.getAddress())) {
//                    throw new IllegalArgumentException("Invalid BSC address format: " + address.getAddress());
//                }
//            }

            // 验证手续费钱包配置
            if (feeWallet != null) {
                feeWallet.validate();
                // 验证手续费钱包地址格式
                if (feeWallet.getAddress() != null && !isValidBscAddress(feeWallet.getAddress())) {
                    throw new IllegalArgumentException("Invalid BSC fee wallet address format: " + feeWallet.getAddress());
                }
            }
        }
    }

    /**
     * 验证BSC地址格式
     */
    private boolean isValidBscAddress(String address) {
        if (address == null || address.trim().isEmpty()) {
            return false;
        }
        // BSC地址以0x开头，长度为42个字符
        return address.matches("^0x[a-fA-F0-9]{40}$");
    }

    /**
     * 是否启用手续费钱包
     */
    public boolean isFeeWalletEnabled() {
        return enabled && feeWallet != null && feeWallet.isEnabled();
    }

    /**
     * 获取手续费钱包私钥
     */
    public String getFeeWalletPrivateKey() {
        return isFeeWalletEnabled() ? feeWallet.getPrivateKey() : null;
    }

    /**
     * 获取手续费钱包地址
     */
    public String getFeeWalletAddress() {
        return isFeeWalletEnabled() ? feeWallet.getAddress() : null;
    }

    // ============ Gas配置访问方法 ============

    /**
     * 获取最大Gas价格
     */
    public long getMaxGasPrice() {
        return feeWallet != null ? feeWallet.getMaxGasPrice() : 20_000_000_000L; // 默认20 gwei
    }

    /**
     * 获取最大Gas限制
     */
    public long getMaxGasLimit() {
        return feeWallet != null ? feeWallet.getMaxGasLimit() : 8_000_000L; // 默认8M gas
    }
}
