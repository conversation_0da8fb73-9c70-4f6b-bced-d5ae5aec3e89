package org.dromara.wallet.config.evm;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * EVM交易补偿配置类
 * 定时扫描和补偿处理EVM待处理交易的相关配置
 *
 * <AUTHOR>
 * @date 2025/7/13
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "evm.compensation")
public class EvmCompensationConfig {

    /**
     * 总开关 - 控制整个补偿功能的启用状态
     */
    private boolean enabled = true;

    /**
     * 重启时执行开关 - 控制是否在应用重启时立即执行一次补偿
     */
    private boolean onStartup = false;

    /**
     * 定时任务表达式 - 定时任务的执行频率，默认每10分钟
     */
    private String cron = "0 */10 * * * *";

    /**
     * 每次处理的最大交易数量，避免一次处理过多数据
     */
    private int batchSize = 100;

    /**
     * 最大重试次数限制
     * 超过此次数的交易将不再进行补偿处理
     */
    private int maxRetryCount = 10;

    /**
     * 补偿处理间隔时间（毫秒）
     * 避免频繁的补偿处理对系统造成压力
     */
    private long compensationIntervalMs = 5 * 60 * 1000; // 5分钟

    /**
     * 异步任务超时时间(秒) - 防止任务卡死
     */
    private int asyncTimeout = 3600;

    /**
     * 是否启用告警通知
     */
    private boolean alertEnabled = true;

    /**
     * 告警阈值 - 当待处理交易数量超过此值时发送告警
     */
    private int alertThreshold = 1000;
}
