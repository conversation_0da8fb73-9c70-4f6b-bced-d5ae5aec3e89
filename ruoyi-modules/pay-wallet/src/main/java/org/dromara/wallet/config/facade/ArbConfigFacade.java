package org.dromara.wallet.config.facade;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.dromara.wallet.config.arb.ArbContractConfig;
import org.dromara.wallet.config.arb.ArbMonitorConfig;
import org.dromara.wallet.config.arb.ArbRpcConfig;
import org.dromara.wallet.config.arb.ArbWalletConfig;
import org.dromara.wallet.domain.dto.MetaMainAddress;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;
import java.util.Set;

/**
 * ARB配置门面类
 * 提供统一的ARB配置访问接口，简化配置使用
 *
 * <AUTHOR>
 * @date 2025-06-30
 */
@Getter
@Slf4j
@Component
public class ArbConfigFacade implements EvmConfigFacade {

    /**
     * 获取钱包配置对象
     */
    @Autowired
    private ArbWalletConfig walletConfig;

    /**
     * 获取RPC配置对象
     */
    @Autowired
    private ArbRpcConfig rpcConfig;

    /**
     * 获取合约配置对象
     */
    @Autowired
    private ArbContractConfig contractConfig;

    /**
     * 获取监控配置对象
     */
    @Autowired
    private ArbMonitorConfig monitorConfig;

    // ============ 基础信息 ============

    /**
     * 是否启用ARB
     */
    public boolean isEnabled() {
        return walletConfig.isEnabled() && rpcConfig.isEnabled();
    }

    /**
     * 获取链名称
     */
    public String getChainName() {
        return "ARB";
    }

    /**
     * 获取链ID
     */
    public long getChainId() {
        return rpcConfig.getChainId();
    }

    /**
     * 获取网络类型
     */
    public String getNetworkType() {
        return rpcConfig.getNetworkType();
    }

    /**
     * 是否为主网
     */
    public boolean isMainnet() {
        return rpcConfig.isMainnet();
    }

    /**
     * 是否为测试网
     */
    public boolean isTestnet() {
        return rpcConfig.isTestnet();
    }

    /**
     * 获取网络名称
     */
    public String getNetworkName() {
        return rpcConfig.getNetworkName();
    }

    // ============ RPC配置 ============

    /**
     * 获取主要RPC端点
     */
    public String getPrimaryEndpoint() {
        return rpcConfig.getPrimaryEndpoint();
    }

    /**
     * 获取可用的RPC端点
     */
    public String getAvailableEndpoint() {
        return rpcConfig.getAvailableEndpoint();
    }

    /**
     * 获取最大Gas价格（安全上限）
     * 从fee-wallet配置中获取，用于防止过高费用
     */
    public long getMaxGasPrice() {
        return walletConfig.getMaxGasPrice();
    }

    /**
     * 获取最大Gas限制（安全上限）
     * 从fee-wallet配置中获取，用于防止过高gas消耗
     */
    public long getMaxGasLimit() {
        return walletConfig.getMaxGasLimit();
    }

    // ============ 合约配置 ============

    /**
     * 获取代币合约地址
     */
    public String getContractAddress(String tokenSymbol) {
        return contractConfig.getContractAddress(tokenSymbol);
    }

    /**
     * 获取代币精度
     */
    public int getContractDecimals(String tokenSymbol) {
        return contractConfig.getContractDecimals(tokenSymbol);
    }

    /**
     * 获取代币精度（别名方法）
     */
    public int getTokenDecimals(String tokenSymbol) {
        return getContractDecimals(tokenSymbol);
    }

    /**
     * 是否支持指定代币
     */
    public boolean isTokenSupported(String tokenSymbol) {
        return contractConfig.getTokenDetail(tokenSymbol) != null &&
            contractConfig.getTokenDetail(tokenSymbol).isEnabled();
    }

    /**
     * 获取启用的代币符号列表
     */
    public Set<String> getEnabledTokenSymbols() {
        return contractConfig.getEnabledTokens().keySet();
    }

    /**
     * 获取启用的代币合约地址列表
     * 用于区块链扫描等场景，返回所有启用且有效的合约地址
     *
     * @return 启用的合约地址列表，如果没有启用的合约则返回空列表
     */
    public List<String> getEnabledContractAddresses() {
        return contractConfig.getEnabledTokens().values().stream()
            .map(ArbContractConfig.TokenDetail::getAddress)
            .filter(address -> address != null && !address.trim().isEmpty())
            .collect(java.util.stream.Collectors.toList());
    }

    // ============ 钱包配置 ============


    /**
     * 获取主钱包地址列表
     */
    public List<MetaMainAddress> getMainAddressList() {
        return walletConfig.getMainAddressList();
    }

    /**
     * 是否启用手续费钱包
     */
    public boolean isFeeWalletEnabled() {
        return walletConfig.getFeeWallet() != null && walletConfig.getFeeWallet().isEnabled();
    }

    /**
     * 获取手续费钱包私钥
     */
    public String getFeeWalletPrivateKey() {
        if (!isFeeWalletEnabled()) {
            return null;
        }
        return walletConfig.getFeeWallet().getPrivateKey();
    }

    /**
     * 获取手续费钱包地址
     */
    public String getFeeWalletAddress() {
        if (!isFeeWalletEnabled()) {
            return null;
        }
        return walletConfig.getFeeWallet().getAddress();
    }

    // ============ 监控配置 ============

    /**
     * 是否启用监控
     */
    public boolean isMonitorEnabled() {
        return monitorConfig.isEnabled();
    }

    /**
     * 获取监控间隔
     */
    public int getMonitorInterval() {
        return monitorConfig.getTransactionMonitorInterval();
    }

    /**
     * 获取区块确认数
     */
    public int getBlockConfirmations() {
        return monitorConfig.getConfirmationBlocks();
    }

    // ============ 自动扫描配置 ============

    /**
     * 是否启用自动扫描功能
     */
    public boolean isAutoScanEnabled() {
        return monitorConfig.isAutoScanEnabled();
    }

    /**
     * 获取自动扫描的起始区块号
     */
    public long getAutoScanStartBlock() {
        return monitorConfig.getAutoScanStartBlock();
    }

    /**
     * 获取自动扫描的扫描周期（毫秒）
     */
    public long getAutoScanPeriod() {
        return monitorConfig.getAutoScanPeriod();
    }

    // ============ 配置摘要 ============

    /**
     * 获取配置摘要
     */
    public String getConfigSummary() {
        return String.format("ARB配置 [网络: %s, 链ID: %d, 端点: %s, 代币数: %d, 手续费钱包: %s]",
            getNetworkType(),
            getChainId(),
            getPrimaryEndpoint(),
            getEnabledTokenSymbols().size(),
            isFeeWalletEnabled() ? "启用" : "禁用"
        );
    }

    /**
     * 获取代币信息摘要
     */
    public String getTokenSummary(String tokenCode) {
        if (!isTokenSupported(tokenCode)) {
            return "不支持的代币: " + tokenCode;
        }

        String address = getContractAddress(tokenCode);
        int decimals = getContractDecimals(tokenCode);
        return String.format("%s (地址: %s, 小数位: %d)", tokenCode, address, decimals);
    }

    /**
     * 获取最小转账金额
     */
    public BigDecimal getMinTransferAmount(String tokenCode) {
        if (contractConfig == null || tokenCode == null) {
            return new BigDecimal("0.01"); // 默认最小转账0.01个单位
        }

        var tokenDetail = contractConfig.getTokenDetail(tokenCode.toLowerCase());
        if (tokenDetail != null) {
            return BigDecimal.valueOf(tokenDetail.getMinAmount());
        }
        return new BigDecimal("0.01");
    }

    /**
     * 获取最大转账金额
     */
    public BigDecimal getMaxTransferAmount(String tokenCode) {
        if (contractConfig == null || tokenCode == null) {
            return new BigDecimal("10000"); // 默认最大转账10000个单位
        }

        var tokenDetail = contractConfig.getTokenDetail(tokenCode.toLowerCase());
        if (tokenDetail != null) {
            return BigDecimal.valueOf(tokenDetail.getMaxAmount());
        }
        return new BigDecimal("10000");
    }

    // ============ 配置验证 ============

    /**
     * 验证配置完整性
     */
    public void validateConfig() {
        try {
            walletConfig.validate();
            rpcConfig.validate();
            contractConfig.validate();
            monitorConfig.validate();
            log.info("ARB配置验证通过");
        } catch (Exception e) {
            log.error("ARB配置验证失败", e);
            throw new IllegalStateException("ARB配置验证失败: " + e.getMessage(), e);
        }
    }
}
