package org.dromara.wallet.config.facade;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.dromara.wallet.config.bsc.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;
import java.util.Set;

/**
 * BSC配置门面类
 * 提供统一的BSC配置访问接口，简化配置使用
 *
 * <AUTHOR>
 * @date 2025-06-27
 */
@Getter
@Slf4j
@Component
public class BscConfigFacade implements EvmConfigFacade {

    /**
     *  获取钱包配置对象
     */
    @Autowired
    private BscWalletConfig walletConfig;

    /**
     *  获取RPC配置对象
     */
    @Autowired
    private BscRpcConfig rpcConfig;

    /**
     *  获取合约配置对象
     */
    @Autowired
    private BscContractConfig contractConfig;

    /**
     *  获取监控配置对象
     */
    @Autowired
    private BscMonitorConfig monitorConfig;

    // ============ 基础信息 ============

    /**
     * 是否启用BSC
     */
    public boolean isEnabled() {
        return walletConfig.isEnabled() && rpcConfig.isEnabled();
    }

    /**
     * 获取链名称
     */
    public String getChainName() {
        return "BSC";
    }

    /**
     * 获取链ID
     */
    public long getChainId() {
        return rpcConfig.getChainId();
    }

    /**
     * 获取网络类型
     */
    public String getNetworkType() {
        return rpcConfig.getNetworkType();
    }

    /**
     * 是否为测试网
     */
    public boolean isTestnet() {
        return rpcConfig.isTestnet();
    }

    /**
     * 获取网络名称
     */
    public String getNetworkName() {
        return rpcConfig.getNetworkName();
    }

    // ============ RPC配置 ============

    /**
     * 获取可用的RPC端点
     */
    public String getAvailableEndpoint() {
        return rpcConfig.getAvailableEndpoint();
    }

    /**
     * 获取主要端点
     */
    public String getPrimaryEndpoint() {
        return rpcConfig.getPrimaryEndpoint();
    }





    // ============ 钱包配置 ============



    /**
     * 是否启用手续费钱包
     */
    public boolean isFeeWalletEnabled() {
        return walletConfig.isFeeWalletEnabled();
    }

    /**
     * 获取手续费钱包地址
     */
    public String getFeeWalletAddress() {
        return walletConfig.getFeeWalletAddress();
    }

    /**
     * 获取手续费钱包私钥
     */
    public String getFeeWalletPrivateKey() {
        return walletConfig.getFeeWalletPrivateKey();
    }

    /**
     * 获取最大Gas价格（安全上限）
     * 从fee-wallet配置中获取，用于防止过高费用
     */
    public long getMaxGasPrice() {
        return walletConfig.getMaxGasPrice();
    }

    /**
     * 获取最大Gas限制（安全上限）
     * 从fee-wallet配置中获取，用于防止过高gas消耗
     */
    public long getMaxGasLimit() {
        return walletConfig.getMaxGasLimit();
    }
    // ============ 合约配置 ============

    /**
     * 获取代币合约地址
     */
    public String getContractAddress(String tokenCode) {
        return contractConfig.getContractAddress(tokenCode);
    }

    /**
     * 获取代币小数位数
     */
    public int getContractDecimals(String tokenCode) {
        return contractConfig.getContractDecimals(tokenCode);
    }

    /**
     * 获取代币精度（别名方法）
     */
    public int getTokenDecimals(String tokenCode) {
        return getContractDecimals(tokenCode);
    }

    /**
     * 检查代币是否启用
     */
    public boolean isTokenEnabled(String tokenCode) {
        return contractConfig.isTokenEnabled(tokenCode);
    }

    /**
     * 验证转账金额
     */
    public boolean isAmountValid(String tokenCode, BigDecimal amount) {
        return contractConfig.isAmountValid(tokenCode, amount);
    }

    /**
     * 获取最小转账金额
     */
    public BigDecimal getMinTransferAmount(String tokenCode) {
        return contractConfig.getMinTransferAmount(tokenCode);
    }

    /**
     * 获取最大转账金额
     */
    public BigDecimal getMaxTransferAmount(String tokenCode) {
        return contractConfig.getMaxTransferAmount(tokenCode);
    }

    /**
     * 获取所有启用的代币符号
     */
    public Set<String> getEnabledTokenSymbols() {
        if (contractConfig.getContracts() == null) {
            return Set.of();
        }
        return contractConfig.getContracts().entrySet().stream()
            .filter(entry -> entry.getValue().isEnabled())
            .map(entry -> entry.getKey().toUpperCase())
            .collect(java.util.stream.Collectors.toSet());
    }

    /**
     * 获取启用的代币合约地址列表
     * 用于区块链扫描等场景，返回所有启用且有效的合约地址
     *
     * @return 启用的合约地址列表，如果没有启用的合约则返回空列表
     */
    public List<String> getEnabledContractAddresses() {
        if (contractConfig.getContracts() == null) {
            return List.of();
        }
        return contractConfig.getContracts().values().stream()
            .filter(BscContractConfig.TokenDetail::isEnabled)
            .map(BscContractConfig.TokenDetail::getAddress)
            .filter(address -> address != null && !address.trim().isEmpty())
            .collect(java.util.stream.Collectors.toList());
    }

    /**
     * 是否启用跨链桥
     */
    public boolean isBridgeEnabled() {
        return contractConfig.isBridgeEnabled();
    }

    /**
     * 是否启用质押
     */
    public boolean isStakingEnabled() {
        return contractConfig.isStakingEnabled();
    }


    // ============ 监控配置 ============

    /**
     * 是否启用监控
     */
    public boolean isMonitorEnabled() {
        return monitorConfig.isEnabled();
    }

    /**
     * 是否启用交易监控
     */
    public boolean isTransactionMonitorEnabled() {
        return monitorConfig.isTransactionMonitorEnabled();
    }

    /**
     * 是否启用余额监控
     */
    public boolean isBalanceMonitorEnabled() {
        return monitorConfig.isBalanceMonitorEnabled();
    }

    /**
     * 是否启用Gas费用监控
     */
    public boolean isGasFeeMonitorEnabled() {
        return monitorConfig.isGasFeeMonitorEnabled();
    }

    /**
     * 获取所需确认数
     */
    public int getRequiredConfirmations() {
        return monitorConfig.getRequiredConfirmations();
    }

    // ============ 自动扫描配置 ============

    /**
     * 是否启用自动扫描功能
     */
    public boolean isAutoScanEnabled() {
        return monitorConfig.isAutoScanEnabled();
    }

    /**
     * 获取自动扫描的起始区块号
     */
    public long getAutoScanStartBlock() {
        return monitorConfig.getAutoScanStartBlock();
    }

    /**
     * 获取自动扫描的扫描周期（毫秒）
     */
    public long getAutoScanPeriod() {
        return monitorConfig.getAutoScanPeriod();
    }

    // ============ 便捷方法 ============

    /**
     * 检查是否支持指定代币
     */
    public boolean isTokenSupported(String tokenCode) {
        return contractConfig.isTokenEnabled(tokenCode) &&
               contractConfig.getContractAddress(tokenCode) != null;
    }

    /**
     * 获取代币信息摘要
     */
    public String getTokenSummary(String tokenCode) {
        if (!isTokenSupported(tokenCode)) {
            return "不支持的代币: " + tokenCode;
        }

        String address = getContractAddress(tokenCode);
        int decimals = getContractDecimals(tokenCode);
        return String.format("%s (地址: %s, 小数位: %d)", tokenCode, address, decimals);
    }

    /**
     * 获取配置摘要
     */
    public String getConfigSummary() {
        return String.format("BSC配置 [网络: %s, 链ID: %d, 端点: %s, 代币数: %d, 手续费钱包: %s]",
            getNetworkType(),
            getChainId(),
            getPrimaryEndpoint(),
            getEnabledTokenSymbols().size(),
            isFeeWalletEnabled() ? "启用" : "禁用"
        );
    }

    // ============ 配置验证 ============

    /**
     * 验证配置完整性
     */
    public void validateConfig() {
        try {
            walletConfig.validate();
            rpcConfig.validate();
            contractConfig.validate();
            monitorConfig.validate();
            log.info("BSC配置验证通过");
        } catch (Exception e) {
            log.error("BSC配置验证失败", e);
            throw new IllegalStateException("BSC配置验证失败: " + e.getMessage(), e);
        }
    }

    /**
     * 验证所有配置
     * @deprecated 使用 validateConfig() 替代
     */
    @Deprecated
    public void validateAll() {
        validateConfig();
    }

    // ============ 获取原始配置对象（高级用法） ============

}
