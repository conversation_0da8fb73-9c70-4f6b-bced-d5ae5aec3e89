package org.dromara.wallet.config.facade;


import java.util.List;
import java.util.Set;

/**
 * EVM配置门面接口
 * 为所有EVM兼容链提供统一的配置访问接口
 *
 * <AUTHOR>
 * @date 2025-06-30
 */
public interface EvmConfigFacade {

    // ============ 基础信息 ============

    /**
     * 是否启用
     */
    boolean isEnabled();

    /**
     * 获取链名称
     */
    String getChainName();

    /**
     * 获取链ID
     */
    long getChainId();

    /**
     * 获取网络类型
     */
    String getNetworkType();

    /**
     * 是否为测试网
     */
    boolean isTestnet();

    /**
     * 获取网络名称
     */
    String getNetworkName();

    // ============ RPC配置 ============

    /**
     * 获取主要RPC端点
     */
    String getPrimaryEndpoint();

    /**
     * 获取可用的RPC端点
     */
    String getAvailableEndpoint();

    // ============ 合约配置 ============

    /**
     * 获取代币合约地址
     */
    String getContractAddress(String tokenSymbol);

    /**
     * 获取代币精度
     */
    int getContractDecimals(String tokenSymbol);

    /**
     * 获取代币精度（别名方法）
     */
    int getTokenDecimals(String tokenSymbol);

    /**
     * 是否支持指定代币
     */
    boolean isTokenSupported(String tokenSymbol);

    /**
     * 获取启用的代币符号列表
     */
    Set<String> getEnabledTokenSymbols();

    /**
     * 获取启用的代币合约地址列表
     * 用于区块链扫描等场景，返回所有启用且有效的合约地址
     *
     * @return 启用的合约地址列表，如果没有启用的合约则返回空列表
     */
    List<String> getEnabledContractAddresses();

    // ============ 钱包配置 ============


    /**
     * 是否启用手续费钱包
     */
    boolean isFeeWalletEnabled();

    /**
     * 获取手续费钱包私钥
     */
    String getFeeWalletPrivateKey();

    /**
     * 获取手续费钱包地址
     */
    String getFeeWalletAddress();

    long getMaxGasLimit();

    long getMaxGasPrice();

    // ============ 自动扫描配置 ============

    /**
     * 是否启用自动扫描功能
     */
    boolean isAutoScanEnabled();

    /**
     * 获取自动扫描的起始区块号
     */
    long getAutoScanStartBlock();

    /**
     * 获取自动扫描的扫描周期（毫秒）
     */
    long getAutoScanPeriod();

    // ============ 配置摘要 ============

    /**
     * 获取配置摘要
     */
    String getConfigSummary();

    /**
     * 获取代币信息摘要
     */
    String getTokenSummary(String tokenCode);
}
