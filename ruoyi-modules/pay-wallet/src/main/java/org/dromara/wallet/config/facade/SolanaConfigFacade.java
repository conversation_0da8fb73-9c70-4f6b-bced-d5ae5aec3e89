package org.dromara.wallet.config.facade;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.dromara.wallet.config.solana.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Set;
import java.util.stream.Collectors;

/**
 * Solana配置门面类
 * 提供统一的Solana配置访问接口，简化配置使用
 *
 * <AUTHOR>
 * @date 2025-06-29
 */
@Getter
@Slf4j
@Component
public class SolanaConfigFacade {

    /**
     * 获取钱包配置对象
     */
    @Autowired
    private SolanaWalletConfig walletConfig;

    /**
     * 获取RPC配置对象
     */
    @Autowired
    private SolanaRpcConfig rpcConfig;

    /**
     * 获取合约配置对象
     */
    @Autowired
    private SolanaContractConfig contractConfig;

    /**
     * 获取监控配置对象
     */
    @Autowired
    private SolanaMonitorConfig monitorConfig;

    /**
     * 获取补偿配置对象
     */
    @Autowired
    private SolanaCompensationConfig compensationConfig;

    // ============ 基础信息 ============

    /**
     * 是否启用Solana
     */
    public boolean isEnabled() {
        return walletConfig != null &&
            walletConfig.isFeeWalletEnabled() &&
            walletConfig.getFeeWalletPrivateKey() != null &&
            !walletConfig.getFeeWalletPrivateKey().trim().isEmpty() &&
            rpcConfig != null &&
            rpcConfig.getRpcList() != null &&
            !rpcConfig.getRpcList().isEmpty();
    }

    /**
     * 获取链名称
     */
    public String getChainName() {
        return "SOLANA";
    }

    /**
     * 获取网络类型
     */
    public String getNetworkType() {
        // 根据RPC URL判断网络类型
        if (rpcConfig != null && rpcConfig.getRpcList() != null && !rpcConfig.getRpcList().isEmpty()) {
            String firstRpc = rpcConfig.getRpcList().get(0);
            if (firstRpc.contains("devnet")) {
                return "devnet";
            } else if (firstRpc.contains("testnet")) {
                return "testnet";
            } else if (firstRpc.contains("mainnet")) {
                return "mainnet";
            }
        }
        return "unknown";
    }

    // ============ 钱包配置 ============

    /**
     * 是否启用手续费钱包
     */
    public boolean isFeeWalletEnabled() {
        return walletConfig != null && walletConfig.isFeeWalletEnabled();
    }

    /**
     * 获取手续费钱包私钥
     */
    public String getFeeWalletPrivateKey() {
        return walletConfig != null ? walletConfig.getFeeWalletPrivateKey() : null;
    }

    /**
     * 获取手续费钱包地址
     */
    public String getFeeWalletAddress() {
        return walletConfig != null ? walletConfig.getFeeWalletAddress() : null;
    }

    // ============ RPC配置 ============

    /**
     * 获取主要RPC端点
     */
    public String getPrimaryEndpoint() {
        if (rpcConfig != null && rpcConfig.getRpcList() != null && !rpcConfig.getRpcList().isEmpty()) {
            return rpcConfig.getRpcList().get(0);
        }
        return null;
    }
    // ============ 合约配置 ============

    /**
     * 检查是否支持指定代币
     */
    public boolean isTokenSupported(String tokenCode) {
        if (contractConfig == null || contractConfig.getContracts() == null || tokenCode == null) {
            return false;
        }
        return contractConfig.getContracts().containsKey(tokenCode.toLowerCase()) ||
            "SOL".equalsIgnoreCase(tokenCode); // SOL是原生代币
    }

    /**
     * 获取代币合约地址
     */
    public String getContractAddress(String tokenCode) {
        if ("SOL".equalsIgnoreCase(tokenCode)) {
            return "So11111111111111111111111111111111111111112"; // SOL原生代币地址
        }
        return contractConfig != null ? contractConfig.getContractAddress(tokenCode) : null;
    }

    /**
     * 获取代币小数位数
     */
    public int getContractDecimals(String tokenCode) {
        if ("SOL".equalsIgnoreCase(tokenCode)) {
            return 9; // SOL默认9位小数
        }
        return contractConfig != null ? contractConfig.getContractDecimals(tokenCode) : 6;
    }

    /**
     * 获取所有启用的代币符号
     */
    public Set<String> getEnabledTokenSymbols() {
        if (contractConfig == null || contractConfig.getContracts() == null) {
            return Set.of("SOL"); // 至少包含原生代币
        }

        Set<String> tokens = contractConfig.getContracts().keySet().stream()
            .map(String::toUpperCase)
            .collect(Collectors.toSet());
        tokens.add("SOL"); // 添加原生代币
        return tokens;
    }

    // ============ 监控配置 ============

    /**
     * 获取连接超时时间
     */
    public int getConnectionTimeout() {
        return monitorConfig != null ? monitorConfig.getConnectionTimeout() : 30;
    }

    /**
     * 获取最大重连次数
     */
    public int getMaxReconnectAttempts() {
        return monitorConfig != null ? monitorConfig.getMaxReconnectAttempts() : 10;
    }

    /**
     * 是否启用监控功能
     */
    public boolean isMonitorEnabled() {
        return monitorConfig != null ? monitorConfig.isEnabled() : true;
    }

    /**
     * 获取初始重连延迟（毫秒）
     */
    public long getInitialReconnectDelay() {
        return monitorConfig != null ? monitorConfig.getInitialReconnectDelay() : 5000L;
    }

    /**
     * 获取最大重连延迟（毫秒）
     */
    public long getMaxReconnectDelay() {
        return monitorConfig != null ? monitorConfig.getMaxReconnectDelay() : 300000L;
    }

    // ============ 补偿配置 ============

    /**
     * 是否启用补偿功能
     */
    public boolean isCompensationEnabled() {
        return compensationConfig != null && compensationConfig.isEnabled();
    }

    /**
     * 获取补偿检查限制
     */
    public int getCompensationCheckLimit() {
        return compensationConfig != null ? compensationConfig.getCheckLimit() : 5;
    }

    // ============ 配置验证 ============

    /**
     * 验证配置完整性
     */
    public void validateConfig() {
        if (!isEnabled()) {
            throw new IllegalStateException("Solana配置未启用或不完整");
        }

        // 验证钱包配置
        if (walletConfig == null || !walletConfig.isFeeWalletEnabled() || walletConfig.getFeeWalletPrivateKey() == null) {
            throw new IllegalStateException("Solana钱包配置缺失");
        }

        // 验证RPC配置
        if (rpcConfig == null || rpcConfig.getRpcList() == null || rpcConfig.getRpcList().isEmpty()) {
            throw new IllegalStateException("Solana RPC配置缺失");
        }

        log.info("Solana配置验证通过");
    }

    /**
     * 获取代币信息摘要
     */
    public String getTokenSummary(String tokenCode) {
        if (!isTokenSupported(tokenCode)) {
            return "不支持的代币: " + tokenCode;
        }

        String address = getContractAddress(tokenCode);
        int decimals = getContractDecimals(tokenCode);
        return String.format("%s (地址: %s, 小数位: %d)", tokenCode, address, decimals);
    }

    /**
     * 获取配置摘要
     */
    public String getConfigSummary() {
        return String.format("Solana配置 [网络: %s, 端点: %s, 代币数: %d, 补偿: %s]",
            getNetworkType(),
            getPrimaryEndpoint(),
            getEnabledTokenSymbols().size(),
            isCompensationEnabled() ? "启用" : "禁用"
        );
    }

    /**
     * 获取健康状态
     */
    public String getHealthStatus() {
        try {
            validateConfig();
            return "✅ 健康";
        } catch (Exception e) {
            return "❌ 异常: " + e.getMessage();
        }
    }
}
