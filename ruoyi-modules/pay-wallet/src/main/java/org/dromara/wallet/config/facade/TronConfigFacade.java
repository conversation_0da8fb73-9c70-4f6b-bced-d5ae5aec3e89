package org.dromara.wallet.config.facade;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.dromara.wallet.config.tron.*;
import org.dromara.wallet.wallet.transfer.alert.TronTransferAlertConfig;
import org.dromara.wallet.wallet.transfer.config.TronTransferMonitorConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;
import java.util.Set;

/**
 * TRON配置门面类
 * 提供统一的TRON配置访问接口，简化配置使用
 *
 * <AUTHOR>
 * @date 2025-06-27
 */
@Getter
@Slf4j
@Component
public class TronConfigFacade {

    /**
     * -- GETTER --
     *  获取钱包配置对象
     */
    @Autowired
    private TronWalletConfig walletConfig;

    /**
     * -- GETTER --
     *  获取统一API配置对象
     */
    @Autowired
    private TronApiConfig apiConfig;

    /**
     * -- GETTER --
     *  获取合约配置对象
     */
    @Autowired
    private TronContractConfig contractConfig;



    /**
     * -- GETTER --
     *  获取监控配置对象
     */
    @Autowired
    private TronMonitorConfig monitorConfig;

    /**
     * -- GETTER --
     *  获取转账监控配置对象
     */
    @Autowired(required = false)
    private TronTransferMonitorConfig transferMonitorConfig;

    /**
     * -- GETTER --
     *  获取转账告警配置对象
     */
    @Autowired(required = false)
    private TronTransferAlertConfig transferAlertConfig;

    // ============ 基础信息 ============

    /**
     * 是否启用TRON
     */
    public boolean isEnabled() {
        return walletConfig.isEnabled() && apiConfig.isEnabled();
    }

    /**
     * 是否启用HTTP API模式
     */
    public boolean isHttpModeEnabled() {
        return apiConfig.isHttpMode();
    }

    /**
     * 是否启用RPC模式
     */
    public boolean isRpcModeEnabled() {
        return apiConfig.isRpcMode();
    }

    /**
     * 获取API模式
     */
    public String getApiMode() {
        return apiConfig.getMode();
    }

    /**
     * 获取链名称
     */
    public String getChainName() {
        return "TRON";
    }

    /**
     * 获取网络类型
     */
    public String getNetworkType() {
        return apiConfig.getNetworkType();
    }

    /**
     * 是否为测试网
     */
    public boolean isTestnet() {
        return apiConfig.isTestnet();
    }

    // ============ API配置 ============

    /**
     * 获取可用的API端点
     */
    public String getAvailableEndpoint() {
        return apiConfig.getAvailableEndpoint();
    }

    /**
     * 获取带API密钥的端点
     */
    public String getAvailableEndpointWithApiKey() {
        return apiConfig.getAvailableEndpointWithApiKey();
    }

    /**
     * 获取主要端点
     */
    public String getPrimaryEndpoint() {
        return apiConfig.getPrimaryEndpoint();
    }

    /**
     * 是否有API密钥
     */
    public boolean hasApiKeys() {
        return apiConfig.hasApiKeys();
    }

    /**
     * 获取API密钥列表
     */
    public List<String> getApiKeys() {
        return apiConfig.getApiKeys();
    }

    // ============ 钱包配置 ============



    /**
     * 是否启用手续费钱包
     */
    public boolean isFeeWalletEnabled() {
        return walletConfig.isFeeWalletEnabled();
    }

    /**
     * 获取手续费钱包地址
     */
    public String getFeeWalletAddress() {
        return walletConfig.getFeeWalletAddress();
    }

    /**
     * 获取手续费钱包私钥
     */
    public String getFeeWalletPrivateKey() {
        return walletConfig.getFeeWalletPrivateKey();
    }

    // ============ 合约配置 ============

    /**
     * 获取代币合约地址
     */
    public String getContractAddress(String tokenCode) {
        return contractConfig.getContractAddress(tokenCode);
    }

    /**
     * 获取代币小数位数
     */
    public int getContractDecimals(String tokenCode) {
        return contractConfig.getContractDecimals(tokenCode);
    }

    /**
     * 获取代币精度（别名方法）
     */
    public int getTokenDecimals(String tokenCode) {
        return getContractDecimals(tokenCode);
    }

    /**
     * 检查代币是否启用
     */
    public boolean isTokenEnabled(String tokenCode) {
        return contractConfig.isTokenEnabled(tokenCode);
    }

    /**
     * 验证转账金额
     */
    public boolean isAmountValid(String tokenCode, BigDecimal amount) {
        return contractConfig.isAmountValid(tokenCode, amount);
    }

    /**
     * 获取最小转账金额
     */
    public BigDecimal getMinTransferAmount(String tokenCode) {
        return contractConfig.getMinTransferAmount(tokenCode);
    }

    /**
     * 获取最大转账金额
     */
    public BigDecimal getMaxTransferAmount(String tokenCode) {
        return contractConfig.getMaxTransferAmount(tokenCode);
    }

    /**
     * 获取所有启用的代币符号
     */
    public Set<String> getEnabledTokenSymbols() {
        if (contractConfig.getContracts() == null) {
            return Set.of();
        }
        return contractConfig.getContracts().entrySet().stream()
            .filter(entry -> entry.getValue().isEnabled())
            .map(entry -> entry.getKey().toUpperCase())
            .collect(java.util.stream.Collectors.toSet());
    }

    /**
     * 获取启用的代币合约地址列表
     * 用于区块链扫描等场景，返回所有启用且有效的合约地址
     *
     * @return 启用的合约地址列表，如果没有启用的合约则返回空列表
     */
    public List<String> getEnabledContractAddresses() {
        if (contractConfig.getContracts() == null) {
            return List.of();
        }
        return contractConfig.getContracts().entrySet().stream()
            .filter(entry -> entry.getValue().isEnabled())
            .map(entry -> entry.getValue().getAddress())
            .filter(address -> address != null && !address.trim().isEmpty())
            .collect(java.util.stream.Collectors.toList());
    }



    // ============ 费用控制配置 ============

    /**
     * 获取最大费用限制
     * 优先级：fee-wallet配置 > 默认值
     */
    public long getMaxFeeLimit() {
        // 优先从fee-wallet获取配置
        if (walletConfig.isFeeWalletEnabled()) {
            return walletConfig.getMaxFeeLimit();
        }
        // 默认值：5 TRX
        return 5000000L;
    }

    /**
     * 获取最大能量燃烧限制
     * 优先级：fee-wallet配置 > 默认值
     */
    public long getMaxEnergyBurn() {
        // 优先从fee-wallet获取配置
        if (walletConfig.isFeeWalletEnabled()) {
            return walletConfig.getMaxEnergyBurn();
        }
        // 默认值：50000
        return 50000L;
    }

    /**
     * 是否启用自动燃烧TRX支付能量
     * 优先级：fee-wallet配置 > 默认值
     */
    public boolean isAutoBurnEnabled() {
        // 优先从fee-wallet获取配置
        if (walletConfig.isFeeWalletEnabled()) {
            return walletConfig.isAutoBurnEnabled();
        }
        // 默认值：启用
        return true;
    }

    /**
     * 是否启用自动燃烧TRX支付带宽
     * 优先级：fee-wallet配置 > 默认值
     */
    public boolean isAutoBurnBandwidthEnabled() {
        // 优先从fee-wallet获取配置
        if (walletConfig.isFeeWalletEnabled()) {
            return walletConfig.isAutoBurnBandwidthEnabled();
        }
        // 默认值：启用
        return true;
    }





    // ============ 监控配置 ============

    /**
     * 是否启用监控
     */
    public boolean isMonitorEnabled() {
        return monitorConfig.isEnabled();
    }

    /**
     * 是否启用交易监控
     */
    public boolean isTransactionMonitorEnabled() {
        return monitorConfig.isTransactionMonitorEnabled();
    }

    /**
     * 是否启用余额监控
     */
    public boolean isBalanceMonitorEnabled() {
        return monitorConfig.isBalanceMonitorEnabled();
    }

    // ============ 便捷方法 ============

    /**
     * 检查是否支持指定代币
     */
    public boolean isTokenSupported(String tokenCode) {
        return contractConfig.isTokenEnabled(tokenCode) &&
               contractConfig.getContractAddress(tokenCode) != null;
    }

    /**
     * 获取代币信息摘要
     */
    public String getTokenSummary(String tokenCode) {
        if (!isTokenSupported(tokenCode)) {
            return "不支持的代币: " + tokenCode;
        }

        String address = getContractAddress(tokenCode);
        int decimals = getContractDecimals(tokenCode);
        return String.format("%s (地址: %s, 小数位: %d)", tokenCode, address, decimals);
    }

    /**
     * 获取配置摘要
     */
    public String getConfigSummary() {
        String mode = getApiMode().toUpperCase();
        return String.format("TRON配置 [模式: %s, 网络: %s, 端点: %s, 代币数: %d, 手续费钱包: %s]",
            mode,
            getNetworkType(),
            getPrimaryEndpoint(),
            getEnabledTokenSymbols().size(),
            isFeeWalletEnabled() ? "启用" : "禁用"
        );
    }

    // ============ 配置验证 ============

    /**
     * 验证配置完整性
     */
    public void validateConfig() {
        try {
            walletConfig.validate();
            apiConfig.validate();
            contractConfig.validate();
            monitorConfig.validate();
            log.info("TRON配置验证通过");
        } catch (Exception e) {
            log.error("TRON配置验证失败", e);
            throw new IllegalStateException("TRON配置验证失败: " + e.getMessage(), e);
        }
    }

    /**
     * 验证所有配置
     * @deprecated 使用 validateConfig() 替代
     */
    @Deprecated
    public void validateAll() {
        validateConfig();
    }

    // ============ 转账监控配置 ============

    /**
     * 是否启用转账监控
     */
    public boolean isTransferMonitorEnabled() {
        return transferMonitorConfig != null && transferMonitorConfig.isEnabled();
    }

    /**
     * 是否启用转账性能监控
     */
    public boolean isTransferPerformanceMonitorEnabled() {
        return transferMonitorConfig != null && transferMonitorConfig.isPerformanceMonitorEnabled();
    }

    /**
     * 是否启用转账异常监控
     */
    public boolean isTransferExceptionMonitorEnabled() {
        return transferMonitorConfig != null && transferMonitorConfig.isExceptionMonitorEnabled();
    }

    /**
     * 获取网络异常重试次数阈值
     */
    public int getNetworkExceptionRetryThreshold() {
        return transferMonitorConfig != null ? transferMonitorConfig.getNetworkExceptionRetryThreshold() : 3;
    }

    /**
     * 获取转账成功率警告阈值
     */
    public double getSuccessRateWarningThreshold() {
        return transferMonitorConfig != null ? transferMonitorConfig.getSuccessRateWarningThreshold() : 95.0;
    }

    /**
     * 获取转账成功率错误阈值
     */
    public double getSuccessRateErrorThreshold() {
        return transferMonitorConfig != null ? transferMonitorConfig.getSuccessRateErrorThreshold() : 90.0;
    }

    /**
     * 获取平均转账时间警告阈值
     */
    public long getAverageTransferTimeWarningThreshold() {
        return transferMonitorConfig != null ? transferMonitorConfig.getAverageTransferTimeWarningThreshold() : 20000;
    }

    /**
     * 获取平均转账时间错误阈值
     */
    public long getAverageTransferTimeErrorThreshold() {
        return transferMonitorConfig != null ? transferMonitorConfig.getAverageTransferTimeErrorThreshold() : 30000;
    }

    // ============ 转账告警配置 ============

    /**
     * 是否启用转账告警
     */
    public boolean isTransferAlertEnabled() {
        return transferAlertConfig != null && transferAlertConfig.isEnabled();
    }

    /**
     * 是否启用转账异常告警
     */
    public boolean isTransferExceptionAlertEnabled() {
        return transferAlertConfig != null && transferAlertConfig.isExceptionAlertEnabled();
    }

    /**
     * 是否启用转账性能告警
     */
    public boolean isTransferPerformanceAlertEnabled() {
        return transferAlertConfig != null && transferAlertConfig.isPerformanceAlertEnabled();
    }

    /**
     * 获取失败率告警阈值
     */
    public double getFailureRateThreshold() {
        return transferAlertConfig != null ? transferAlertConfig.getFailureRateThreshold() : 10.0;
    }

    /**
     * 获取转账耗时告警阈值
     */
    public long getTransferTimeThreshold() {
        return transferAlertConfig != null ? transferAlertConfig.getTransferTimeThreshold() : 30000;
    }

    /**
     * 是否启用钉钉告警
     */
    public boolean isDingTalkAlertEnabled() {
        return transferAlertConfig != null && transferAlertConfig.isDingTalkEnabled();
    }

    /**
     * 获取钉钉Webhook URL
     */
    public String getDingTalkWebhookUrl() {
        return transferAlertConfig != null ? transferAlertConfig.getDingTalkWebhookUrl() : null;
    }

    // ============ 能量代理API配置 ============

    /**
     * 是否启用能量代理API
     * 优先级：fee-wallet配置 > 默认值
     */
    public boolean isEnergyProxyEnabled() {
        // 优先从fee-wallet获取配置
        if (walletConfig.isFeeWalletEnabled()) {
            return walletConfig.isEnergyProxyEnabled();
        }
        // 默认值：禁用
        return false;
    }

    /**
     * 获取能量代理API地址
     * 优先级：fee-wallet配置 > 默认值
     */
    public String getEnergyProxyUrl() {
        // 优先从fee-wallet获取配置
        if (walletConfig.isFeeWalletEnabled() && walletConfig.isEnergyProxyEnabled()) {
            return walletConfig.getEnergyProxyUrl();
        }
        // 默认值：空字符串
        return "";
    }

    /**
     * 获取能量代理API密钥参数名
     * 优先级：fee-wallet配置 > 默认值
     */
    public String getEnergyProxyKey() {
        // 优先从fee-wallet获取配置
        if (walletConfig.isFeeWalletEnabled() && walletConfig.isEnergyProxyEnabled()) {
            return walletConfig.getEnergyProxyKey();
        }
        // 默认值：key
        return "key";
    }

    /**
     * 获取能量代理API密钥值
     * 优先级：fee-wallet配置 > 默认值
     */
    public String getEnergyProxyValue() {
        // 优先从fee-wallet获取配置
        if (walletConfig.isFeeWalletEnabled() && walletConfig.isEnergyProxyEnabled()) {
            return walletConfig.getEnergyProxyValue();
        }
        // 默认值：空字符串
        return "";
    }

    /**
     * 获取能量代理小时参数
     * 优先级：fee-wallet配置 > 默认值
     */
    public int getEnergyProxyHour() {
        // 优先从fee-wallet获取配置
        if (walletConfig.isFeeWalletEnabled() && walletConfig.isEnergyProxyEnabled()) {
            return walletConfig.getEnergyProxyHour();
        }
        // 默认值：1小时
        return 1;
    }

    // ============ 获取原始配置对象（高级用法） ============

    /**
     * 获取转账监控配置对象
     */
    public TronTransferMonitorConfig getTransferMonitorConfig() {
        return transferMonitorConfig;
    }

    /**
     * 获取转账告警配置对象
     */
    public TronTransferAlertConfig getTransferAlertConfig() {
        return transferAlertConfig;
    }

    // ============ 自动扫描配置 ============

    /**
     * 是否启用自动扫描功能
     */
    public boolean isAutoScanEnabled() {
        return monitorConfig.isAutoScanEnabled();
    }

    /**
     * 获取自动扫描的起始区块号
     */
    public long getAutoScanStartBlock() {
        return monitorConfig.getAutoScanStartBlock();
    }

    /**
     * 获取自动扫描的扫描周期（毫秒）
     */
    public long getAutoScanPeriod() {
        return monitorConfig.getAutoScanPeriod();
    }

}
