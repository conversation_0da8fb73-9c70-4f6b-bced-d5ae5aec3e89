package org.dromara.wallet.config.solana;

import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.dromara.wallet.config.SolanaCoinType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.DependsOn;


/**
 * SolCoinType枚举配置初始化
 *
 * <AUTHOR>
 * @date 2025/5/9
 */
@Slf4j
@Configuration
@DependsOn("solanaContractConfig") // 确保SolContractConfig先初始化
public class SolanaCoinTypeConfig {

    private final SolanaContractConfig contractConfig;

    @Autowired
    public SolanaCoinTypeConfig(SolanaContractConfig contractConfig) {
        this.contractConfig = contractConfig;
    }

        /**
     * 初始化SolCoinType枚举的合约地址配置
     */
    @PostConstruct
    public void init() {
        log.info("开始初始化SolCoinType枚举的合约配置");

        if (contractConfig == null) {
            log.error("SolContractConfig配置为null，合约地址将无法获取");
            return;
        }

        // 给配置加载一些时间，如果第一次为空，稍后重试
        if (contractConfig.getContracts() == null || contractConfig.getContracts().isEmpty()) {
            log.warn("solanacontract.contracts配置为空，500ms后重试");
            try {
                Thread.sleep(500);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }

        if (contractConfig.getContracts() == null || contractConfig.getContracts().isEmpty()) {
            log.error("solanacontract.contracts配置仍然为空，请检查application.yml文件中的配置");
            log.error("应该包含类似以下配置:");
            log.error("sol:");
            log.error("  contract:");
            log.error("    contracts:");
            log.error("      usdt:");
            log.error("        address: \"Gh9ZwEmdLJ8DscKNTkTqPbNwLNNBjuSzaG9Vp2KGtKJr\"");
            log.error("        decimals: 6");
            return;
        }

        log.info("solanacontract.contracts配置: {}", contractConfig.getContracts().keySet());

        SolanaCoinType.setContractConfig(contractConfig);

        // 验证配置是否正确加载
        for (SolanaCoinType coinType : SolanaCoinType.values()) {
            String address = coinType.getContractAddress();
            log.info("币种 {} 的合约地址: {}", coinType.getCode(), address);
        }

        log.info("SolCoinType枚举合约配置初始化完成");
    }
}
