package org.dromara.wallet.config.solana;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * Solana定时补偿配置类
 * 定时扫描遗漏交易的相关配置
 *
 * <AUTHOR> Assistant
 * @date 2025-01-27
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "solana.compensation")
public class SolanaCompensationConfig {

    /**
     * 总开关 - 控制整个补偿功能的启用状态
     */
    private boolean enabled = true;

    /**
     * 重启时执行开关 - 控制是否在应用重启时立即执行一次补偿
     */
    private boolean onStartup = false;

    /**
     * 定时任务表达式 - 定时任务的执行频率，默认每30分钟
     */
    private String cron = "0 0 */2 * * *";

    /**
     * 检查数量限制 - 每个地址检查的最近交易数量，避免扫描过多历史数据
     */
    private int checkLimit = 5;

    /**
     * 异步任务超时时间(秒) - 防止任务卡死
     */
    private int asyncTimeout = 3600;
}
