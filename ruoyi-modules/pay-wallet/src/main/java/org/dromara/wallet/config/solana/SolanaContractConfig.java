package org.dromara.wallet.config.solana;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.Map;

/**
 * Solana 合约配置
 *
 * <AUTHOR>
 * @date 2023/5/30
 **/
@Data
@Configuration
@ConfigurationProperties(prefix = "solana.contract")
public class SolanaContractConfig {

    /**
     * 代币合约地址映射
     */
    private Map<String, TokenDetail> contracts;

    /**
     * 根据代币代码获取合约地址
     *
     * @param code 代币代码
     * @return 合约地址
     */
    public String getContractAddress(String code) {
        if (contracts == null || code == null) {
            return null;
        }
        TokenDetail detail = contracts.get(code.toLowerCase());
        return detail != null ? detail.getAddress() : null;
    }

    /**
     * 根据代币代码获取小数位数
     *
     * @param code 代币代码
     * @return 小数位数，如果未配置则默认为6
     */
    public int getContractDecimals(String code) {
        if (contracts == null || code == null) {
            return 6; // Default if no contracts map or code is null
        }
        TokenDetail detail = contracts.get(code.toLowerCase());
        // If detail is null or decimals is not explicitly set (e.g. 0), default to 6.
        // Assuming 0 is not a valid decimal count from yml for SPL tokens.
        return detail != null && detail.getDecimals() > 0 ? detail.getDecimals() : 6;
    }

    @Getter
    @Setter
    public static class TokenDetail {
        private String address;
        private int decimals;
    }
}
