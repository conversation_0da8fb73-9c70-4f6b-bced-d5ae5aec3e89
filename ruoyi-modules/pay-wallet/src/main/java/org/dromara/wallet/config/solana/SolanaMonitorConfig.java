package org.dromara.wallet.config.solana;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * Solana监控配置 - 优化版本
 * 包含监控开关和重连配置
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "solana.monitor")
public class SolanaMonitorConfig {

    // ============ 监控总开关 ============

    /**
     * 是否启用监控功能
     * true: 启用监控，应用启动时自动建立WebSocket连接并开始监控
     * false: 禁用监控，不会建立任何连接
     */
    private boolean enabled = true;

    // ============ 连接基础配置 ============

    /**
     * WebSocket连接超时时间（秒）
     */
    private int connectionTimeout = 30;

    /**
     * 最大重连次数
     * 自动重连总是启用，这里配置最大重连次数
     */
    private int maxReconnectAttempts = 10;

    /**
     * 初始重连间隔（毫秒）
     * 实际重连使用指数退避策略，这是初始延迟时间
     */
    private long initialReconnectDelay = 5000;

    /**
     * 最大重连间隔（毫秒）
     * 指数退避的最大延迟时间
     */
    private long maxReconnectDelay = 300000;
}
