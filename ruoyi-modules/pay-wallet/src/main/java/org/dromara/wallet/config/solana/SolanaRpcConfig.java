package org.dromara.wallet.config.solana;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import org.p2p.solanaj.rpc.RpcClient;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.List;
import java.util.concurrent.ThreadLocalRandom;

/**
 * Solana RPC配置
 *
 * <AUTHOR>
 * @date 2023/5/30
 **/
@Data
@Configuration
@ConfigurationProperties(prefix = "solana.rpc")
public class SolanaRpcConfig {

    // 从配置文件中读取RPC URL列表
    private List<String> rpcList;

    // 从配置文件中读取Websocket URL
    private String websocketUrl;

    /**
     * 获取RPC客户端实例
     * 使用轮询策略从配置的RPC节点列表中选择一个节点
     *
     * @return RPC客户端实例
     */
    public RpcClient getRpcClient() {
        if (rpcList == null || rpcList.isEmpty()) {
            throw new IllegalStateException("Solana RPC节点列表未配置");
        }
        // 使用线程安全的随机选择节点
        int index = ThreadLocalRandom.current().nextInt(rpcList.size());
        return new RpcClient(rpcList.get(index));
    }
}
