package org.dromara.wallet.config.solana;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import org.dromara.common.tenant.helper.TenantHelper;
import org.dromara.wallet.domain.dto.MetaMainAddress;
import org.dromara.wallet.wallet.exception.WalletException;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.List;
import java.util.Objects;

/**
 * Solana钱包配置
 *
 * <AUTHOR>
 * @date 2023/5/30
 **/
@Data
@Configuration
@ConfigurationProperties(prefix = "solana.wallet")
public class SolanaWalletConfig {


    // 主钱包地址列表
    private List<MetaMainAddress> mainAddressList;

    /**
     * 是否自动创建代币账户
     * 当钱包没有某个SPL代币的关联账户时，是否自动创建
     */
    private boolean autoCreateTokenAccount = false;

    /**
     * 手续费钱包配置
     */
    private FeeWalletConfig feeWallet;

    /**
     * 通过指定tenantId获取对应主钱包（归集钱包）
     *
     * @param tenantId 租户ID
     * @return 主钱包地址(归集钱包地址)，如果不存在则抛出异常
     */
    public MetaMainAddress getMainAddress(String tenantId) {
        if (mainAddressList == null || mainAddressList.isEmpty()) {
            throw new WalletException("Solana主钱包地址列表为空");
        }

        for (MetaMainAddress item : mainAddressList) {
            if (Objects.equals(tenantId, item.getTenantId())) {
                return item;
            }
        }
        throw new WalletException("租户tenantId:" + tenantId + ",对应Solana主钱包地址不存在");
    }

    /**
     * 通过当前租户获取对应主钱包（归集钱包）
     *
     * @return 主钱包地址(归集钱包地址)，如果不存在则抛出异常
     */
    public MetaMainAddress getMainAddress() {
        return getMainAddress(TenantHelper.getTenantId());
    }


    /**
     * 手续费钱包配置
     */
    @Getter
    @Setter
    public static class FeeWalletConfig {
        /**
         * 是否启用手续费钱包
         */
        private boolean enabled = true;

        /**
         * 手续费钱包私钥
         */
        private String privateKey;

        /**
         * 手续费钱包地址
         */
        private String address;

        /**
         * 最小SOL余额阈值（低于此值时需要补充）
         */
        private double minSolBalance = 0.1;

        /**
         * 每次补充的SOL数量
         */
        private double solRefillAmount = 0.5;

        /**
         * 验证手续费钱包配置
         */
        public void validate() {
            if (enabled) {
                if (privateKey == null || privateKey.trim().isEmpty()) {
                    throw new IllegalArgumentException("Fee wallet private key is required when fee wallet is enabled");
                }
                if (address == null || address.trim().isEmpty()) {
                    throw new IllegalArgumentException("Fee wallet address is required when fee wallet is enabled");
                }
                if (minSolBalance <= 0) {
                    throw new IllegalArgumentException("Min SOL balance must be positive");
                }
                if (solRefillAmount <= 0) {
                    throw new IllegalArgumentException("SOL refill amount must be positive");
                }
            }
        }
    }

    /**
     * 是否启用手续费钱包
     */
    public boolean isFeeWalletEnabled() {
        return feeWallet != null && feeWallet.isEnabled();
    }

    /**
     * 获取手续费钱包私钥
     */
    public String getFeeWalletPrivateKey() {
        return isFeeWalletEnabled() ? feeWallet.getPrivateKey() : null;
    }

    /**
     * 获取手续费钱包地址
     */
    public String getFeeWalletAddress() {
        return isFeeWalletEnabled() ? feeWallet.getAddress() : null;
    }
}
