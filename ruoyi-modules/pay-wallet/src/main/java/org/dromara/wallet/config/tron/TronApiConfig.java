package org.dromara.wallet.config.tron;

import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.ClientHttpRequestFactory;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;

import java.util.List;
import java.util.concurrent.ThreadLocalRandom;

/**
 * TRON统一API配置
 * 合并RPC和HTTP配置，通过mode字段控制使用方式
 *
 * <AUTHOR>
 * @date 2025-07-15
 */
@Slf4j
@Getter
@Setter
@Configuration
@ConfigurationProperties(prefix = "tron.api")
public class TronApiConfig {

    /**
     * API模式：rpc 或 http
     */
    private String mode = "http";

    /**
     * 是否启用
     */
    private boolean enabled = true;

    /**
     * 网络类型：MAINNET/TESTNET
     */
    private String networkType = "TESTNET";

    /**
     * 主要API端点
     */
    private String primaryEndpoint = "https://api.shasta.trongrid.io";

    /**
     * 备用API端点列表
     */
    private List<String> backupEndpoints;

    /**
     * TronScan API端点（用于查询交易状态等）
     */
    private String scanEndpoint = "https://shastapi.tronscan.org";

    /**
     * API密钥列表
     */
    private List<String> apiKeys;

    /**
     * 连接超时时间（毫秒）
     * 用于建立TCP连接的时间限制，30秒是HTTP客户端的标准配置
     * 与其他区块链配置保持一致，平衡了稳定性和响应性
     */
    private int connectionTimeout = 30000;

    /**
     * 读取超时时间（毫秒）
     * 等待服务器响应数据的时间限制，适合TRON API的响应时间特性
     * 为复杂查询（如历史交易、区块数据）提供足够的等待时间
     */
    private int readTimeout = 60000;

    /**
     * 用户代理字符串（HTTP模式专用）
     * 帮助TronGrid API识别客户端类型，有助于API统计和问题排查
     * 可能影响API限制策略，对TRON API调用有实际意义
     */
    private String userAgent = "TronWallet/1.0";

    // ============ 模式判断方法 ============

    /**
     * 是否为HTTP模式
     */
    public boolean isHttpMode() {
        return "http".equalsIgnoreCase(mode);
    }

    /**
     * 是否为RPC模式
     */
    public boolean isRpcMode() {
        return "rpc".equalsIgnoreCase(mode);
    }



    // ============ 网络类型判断 ============

    /**
     * 是否为主网
     */
    public boolean isMainnet() {
        return "MAINNET".equalsIgnoreCase(networkType);
    }

    /**
     * 是否为测试网
     */
    public boolean isTestnet() {
        return "TESTNET".equalsIgnoreCase(networkType);
    }

    // ============ 端点管理 ============

    /**
     * 获取可用的API端点（带故障转移）
     */
    public String getAvailableEndpoint() {
        // 优先使用主要端点
        if (primaryEndpoint != null && !primaryEndpoint.trim().isEmpty()) {
            return primaryEndpoint;
        }

        // 如果主要端点不可用，使用备用端点
        if (backupEndpoints != null && !backupEndpoints.isEmpty()) {
            int index = ThreadLocalRandom.current().nextInt(backupEndpoints.size());
            return backupEndpoints.get(index);
        }

        throw new IllegalStateException("No available TRON API endpoints configured");
    }

    /**
     * 获取随机备用端点
     */
    public String getRandomBackupEndpoint() {
        if (backupEndpoints == null || backupEndpoints.isEmpty()) {
            return primaryEndpoint;
        }
        int index = ThreadLocalRandom.current().nextInt(backupEndpoints.size());
        return backupEndpoints.get(index);
    }

    /**
     * 获取可用的端点URL
     * 注意：API密钥通过HTTP头传递，不拼接到URL
     */
    public String getAvailableEndpointWithApiKey() {
        return getAvailableEndpoint();
    }

    // ============ API密钥管理 ============

    /**
     * 是否有API密钥
     */
    public boolean hasApiKeys() {
        return apiKeys != null && !apiKeys.isEmpty();
    }

    /**
     * 获取第一个API密钥
     */
    public String getPrimaryApiKey() {
        return (apiKeys != null && !apiKeys.isEmpty()) ? apiKeys.get(0) : null;
    }

    /**
     * 获取随机API密钥
     */
    public String getRandomApiKey() {
        if (!hasApiKeys()) {
            return null;
        }
        if (apiKeys.size() == 1) {
            return apiKeys.get(0);
        }
        int index = ThreadLocalRandom.current().nextInt(apiKeys.size());
        return apiKeys.get(index);
    }

    // ============ RestTemplate Bean（HTTP模式专用）============

    /**
     * 创建专用的RestTemplate Bean（仅在HTTP模式下创建）
     */
    @Bean("tronRestTemplate")
    public RestTemplate tronRestTemplate() {
        if (!isHttpMode()) {
            log.info("TRON配置为RPC模式，跳过RestTemplate创建");
            return null;
        }

        RestTemplate restTemplate = new RestTemplate();

        // 配置HTTP客户端工厂
        ClientHttpRequestFactory factory = createHttpRequestFactory();
        restTemplate.setRequestFactory(factory);

        log.info("TRON HTTP API RestTemplate配置完成: endpoint={}, timeout={}ms",
            primaryEndpoint, connectionTimeout);

        return restTemplate;
    }

    /**
     * 创建HTTP请求工厂
     */
    private ClientHttpRequestFactory createHttpRequestFactory() {
        SimpleClientHttpRequestFactory factory = new SimpleClientHttpRequestFactory();
        factory.setConnectTimeout(connectionTimeout);
        factory.setReadTimeout(readTimeout);
        return factory;
    }

    // ============ 配置验证 ============

    /**
     * 验证配置
     */
    public void validate() {
        if (!enabled) {
            return;
        }

        if (mode == null || (!isHttpMode() && !isRpcMode())) {
            throw new IllegalStateException("TRON API模式配置无效，必须为 'rpc' 或 'http'");
        }

        if (primaryEndpoint == null || primaryEndpoint.trim().isEmpty()) {
            throw new IllegalStateException("TRON API主端点未配置");
        }

        if (connectionTimeout <= 0 || readTimeout <= 0) {
            throw new IllegalStateException("TRON API超时配置无效");
        }

        log.info("TRON API配置验证通过: 模式={}, 端点={}", mode, primaryEndpoint);
    }

    // ============ 配置摘要 ============

    /**
     * 获取配置摘要
     */
    public String getConfigSummary() {
        return String.format("TRON API [模式: %s, 网络: %s, 端点: %s, 超时: %dms, API密钥: %s]",
            mode.toUpperCase(),
            networkType,
            primaryEndpoint,
            connectionTimeout,
            hasApiKeys() ? "已配置" : "未配置"
        );
    }

}
