package org.dromara.wallet.config.tron;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * TRON监控配置
 * 扁平化配置 - 监控和告警相关配置
 *
 * <AUTHOR>
 * @date 2025-06-27
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "tron.monitor")
public class TronMonitorConfig {

    /**
     * 是否启用监控
     */
    private boolean enabled = true;

    /**
     * 是否启用监控
     */
    public boolean isEnabled() {
        return enabled;
    }

    // ============ 连接监控配置 ============

    /**
     * WebSocket连接超时时间（秒）
     */
    private int connectionTimeout = 30;

    /**
     * 最大重连次数
     */
    private int maxReconnectAttempts = 5;

    /**
     * 重连间隔（秒）
     */
    private int reconnectInterval = 10;

    /**
     * 是否启用自动重连
     */
    private boolean enableAutoReconnect = true;

    /**
     * 连接健康检查间隔（秒）
     */
    private int healthCheckInterval = 60;

    // ============ 交易监控配置 ============

    /**
     * 是否启用交易监控
     */
    private boolean transactionMonitorEnabled = true;

    /**
     * 是否启用交易监控
     */
    public boolean isTransactionMonitorEnabled() {
        return transactionMonitorEnabled;
    }

    /**
     * 交易确认超时时间（秒）
     */
    private int transactionTimeout = 300; // 5分钟

    /**
     * 交易状态检查间隔（秒）
     */
    private int transactionCheckInterval = 10;

    /**
     * 最大交易确认等待时间（秒）
     */
    private int maxConfirmationWaitTime = 600; // 10分钟

    /**
     * 交易失败重试次数
     */
    private int transactionRetryCount = 3;

    // ============ 余额监控配置 ============

    /**
     * 是否启用余额监控
     */
    private boolean balanceMonitorEnabled = true;

    /**
     * 是否启用余额监控
     */
    public boolean isBalanceMonitorEnabled() {
        return balanceMonitorEnabled;
    }

    // ============ 自动扫描配置 ============

    /**
     * 是否启用自动扫描功能
     * 启用后，应用启动时会自动开始TRON链的区块扫描任务
     */
    private boolean autoScanEnabled = true;

    /**
     * 是否启用自动扫描功能
     */
    public boolean isAutoScanEnabled() {
        return autoScanEnabled;
    }

    /**
     * 自动扫描的起始区块号
     * 如果为0或负数，则从Redis中恢复上次扫描进度
     * 如果Redis中没有进度记录，则从最新区块开始扫描
     */
    private long autoScanStartBlock = 0L;

    /**
     * 自动扫描的扫描周期（毫秒）
     * TRON出块时间约3秒，建议设置为15秒
     */
    private long autoScanPeriod = 15000L;

    /**
     * 余额检查间隔（秒）
     */
    private int balanceCheckInterval = 300; // 5分钟

    /**
     * TRX余额低阈值
     */
    private double lowTrxBalanceThreshold = 10.0;

    /**
     * 能量余额低阈值
     */
    private long lowEnergyBalanceThreshold = 10000L;

    /**
     * 带宽余额低阈值
     */
    private long lowBandwidthBalanceThreshold = 1000L;

    // ============ 告警配置 ============

    /**
     * 是否启用告警
     */
    private boolean alertEnabled = true;

    /**
     * 告警发送间隔（秒）- 防止重复告警
     */
    private int alertInterval = 3600; // 1小时

    /**
     * 是否启用邮件告警
     */
    private boolean emailAlertEnabled = false;

    /**
     * 是否启用短信告警
     */
    private boolean smsAlertEnabled = false;

    /**
     * 是否启用钉钉告警
     */
    private boolean dingTalkAlertEnabled = true;

    /**
     * 钉钉机器人Webhook URL
     */
    private String dingTalkWebhookUrl;

    /**
     * 验证监控配置
     */
    public void validate() {
        if (enabled) {
            if (connectionTimeout <= 0) {
                throw new IllegalArgumentException("Connection timeout must be positive");
            }
            if (transactionCheckInterval <= 0) {
                throw new IllegalArgumentException("Transaction check interval must be positive");
            }
        }
    }

    // ============ 性能监控配置 ============

    /**
     * 是否启用性能监控
     */
    private boolean performanceMonitorEnabled = true;

    /**
     * API响应时间阈值（毫秒）
     */
    private long apiResponseTimeThreshold = 5000; // 5秒

    /**
     * 错误率阈值（百分比）
     */
    private double errorRateThreshold = 5.0; // 5%

    /**
     * 性能统计窗口时间（秒）
     */
    private int performanceWindowTime = 300; // 5分钟

    // ============ 日志配置 ============

    /**
     * 是否启用详细日志
     */
    private boolean verboseLoggingEnabled = false;

    /**
     * 是否记录API请求日志
     */
    private boolean apiRequestLoggingEnabled = false;

    /**
     * 是否记录交易详情日志
     */
    private boolean transactionLoggingEnabled = true;

    /**
     * 日志保留天数
     */
    private int logRetentionDays = 30;

    // ============ 补偿任务配置 ============

    /**
     * 是否启用补偿任务
     */
    private boolean compensationEnabled = true;

    /**
     * 补偿任务执行间隔（cron表达式）
     */
    private String compensationCron = "0 0 */2 * * *"; // 每2小时执行一次

    /**
     * 补偿任务检查的交易数量限制
     */
    private int compensationCheckLimit = 10;

    /**
     * 补偿任务超时时间（秒）
     */
    private int compensationTimeout = 1800; // 30分钟

    // ============ 验证方法 ============


}
