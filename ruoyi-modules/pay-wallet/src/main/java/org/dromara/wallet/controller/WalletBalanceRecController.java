package org.dromara.wallet.controller;

import cn.dev33.satoken.annotation.SaIgnore;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.dromara.wallet.config.ChainType;
import org.dromara.wallet.domain.bo.WalletCoinRecBo;
import org.dromara.wallet.domain.dto.WalletTransferBo;
import org.dromara.wallet.domain.vo.WalletCoinRecVo;
import org.dromara.wallet.service.IWalletCoinRecService;
import org.dromara.wallet.service.IWalletTransferService;
import org.dromara.wallet.wallet.transfer.dto.UnifiedTransferResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;

/**
 * 多链用户钱包代币余额记录控制器
 *
 * <AUTHOR>
 * @date 2025/1/27
 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/coinrec")
public class WalletBalanceRecController extends BaseController {

    private final IWalletCoinRecService walletCoinRecService;
    private final IWalletTransferService walletTransferService;

    /**
     * 归集
     *
     * @param walletTransferBo 归集请求参数
     */
//    @SaCheckPermission("wallet:transfer:collect")
    @Log(title = "区块链钱包归集", businessType = BusinessType.COLLECT)
    @PostMapping("/collect")
    public R<Void> collect(@RequestBody WalletTransferBo walletTransferBo) {
        try {

            // 归集操作，toAddress应该为空，从配置获取
            if (walletTransferBo.getToAddress() != null && !walletTransferBo.getToAddress().trim().isEmpty()) {
                log.warn("归集操作中检测到toAddress不为空，将被忽略: {}", walletTransferBo.getToAddress());
                walletTransferBo.setToAddress(null);
            }

            // 调用统一转账服务执行归集
            UnifiedTransferResult result = walletTransferService.executeTransfer(walletTransferBo);

            if (result.isSuccess()) {
                return R.ok("归集成功，交易哈希: " + result.getTxHash());
            } else {
                return R.fail("归集失败: " + result.getErrorMessage());
            }
        } catch (Exception e) {
            log.error("归集操作异常: from={}, chain={}, error={}",
                walletTransferBo.getFromAddress(), walletTransferBo.getChainType(), e.getMessage(), e);
            return R.fail("归集失败: " + e.getMessage());
        }
    }

    /**
     * 转账
     *
     * @param walletTransferBo 转账请求参数
     */
//    @SaCheckPermission("wallet:transfer:transfer")
    @Log(title = "区块链钱包转账", businessType = BusinessType.TRANSFER)
    @PostMapping("/transfer")
    public R<Void> transfer(@RequestBody WalletTransferBo walletTransferBo) {

        //todo 需要通过google验证器校验


        try {
            // 验证转账必需的目标地址
            if (walletTransferBo.getToAddress() == null || walletTransferBo.getToAddress().trim().isEmpty()) {
                return R.fail("转账操作的目标地址不能为空");
            }

            // 调用统一转账服务执行转账
            UnifiedTransferResult result = walletTransferService.executeTransfer(walletTransferBo);

            if (result.isSuccess()) {
                return R.ok("转账成功，交易哈希: " + result.getTxHash());
            } else {
                return R.fail("转账失败: " + result.getErrorMessage());
            }
        } catch (Exception e) {
            log.error("转账操作异常: from={}, to={}, chain={}, error={}",
                walletTransferBo.getFromAddress(), walletTransferBo.getToAddress(),
                walletTransferBo.getChainType(), e.getMessage(), e);
            return R.fail("转账失败: " + e.getMessage());
        }
    }

    /**
     * 查询多链用户钱包代币余额记录列表
     */
//    @SaCheckPermission("wallet:coinrec:list")
    @SaIgnore
    @GetMapping("/list")
    public TableDataInfo<WalletCoinRecVo> list(WalletCoinRecBo bo, PageQuery pageQuery) {
        return walletCoinRecService.queryPageList(bo, pageQuery);
    }

    /**
     * 查询每个钱包地址和代币组合的最新记录（分页）
     */
//    @SaCheckPermission("wallet:coinrec:list")
    @SaIgnore
    @GetMapping("/latest/page")
    public TableDataInfo<WalletCoinRecVo> latestPage(WalletCoinRecBo bo, PageQuery pageQuery) {
        return walletCoinRecService.queryLatestRecordsPage(bo, pageQuery);
    }

    /**
     * 导出多链用户钱包代币余额记录列表
     */
//    @SaCheckPermission("wallet:coinrec:export")
    @SaIgnore
    @Log(title = "多链用户钱包代币余额记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public List<WalletCoinRecVo> export(WalletCoinRecBo bo) {
        return walletCoinRecService.queryList(bo);
    }

    /**
     * 获取多链用户钱包代币余额记录详细信息
     *
     * @param id 主键
     */
//    @SaCheckPermission("wallet:coinrec:query")
    @GetMapping("/{id}")
    public R<WalletCoinRecVo> getInfo(@NotNull(message = "主键不能为空") @PathVariable Long id) {
        return R.ok(walletCoinRecService.queryById(id));
    }

    /**
     * 新增多链用户钱包代币余额记录
     */
//    @SaCheckPermission("wallet:coinrec:add")
    @Log(title = "多链用户钱包代币余额记录", businessType = BusinessType.INSERT)
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody WalletCoinRecBo bo) {
        return toAjax(walletCoinRecService.insertByBo(bo));
    }

    /**
     * 修改多链用户钱包代币余额记录
     */
//    @SaCheckPermission("wallet:coinrec:edit")
    @Log(title = "多链用户钱包代币余额记录", businessType = BusinessType.UPDATE)
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody WalletCoinRecBo bo) {
        return toAjax(walletCoinRecService.updateByBo(bo));
    }

    /**
     * 删除多链用户钱包代币余额记录
     *
     * @param ids 主键串
     */
//    @SaCheckPermission("wallet:coinrec:remove")
    @Log(title = "多链用户钱包代币余额记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空") @PathVariable Long[] ids) {
        return toAjax(walletCoinRecService.deleteWithValidByIds(Arrays.asList(ids), true));
    }

    /**
     * 扫描指定地址和区块链的余额并新增记录
     *
     * @param address   钱包地址
     * @param chainType 区块链类型
     * @return 扫描结果
     */
    @Log(title = "扫描钱包余额", businessType = BusinessType.INSERT)
    @PostMapping("/scan/{address}/{chainType}")
    public R<String> scanWalletBalance(@PathVariable @NotEmpty(message = "钱包地址不能为空") String address,
                                       @PathVariable @NotNull(message = "区块链类型不能为空") ChainType chainType) {
        try {
            log.info("开始扫描钱包余额: address={}, chainType={}", address, chainType);

            // 根据区块链类型调用对应的余额扫描服务
            switch (chainType) {
                case TRON:
                case BSC:
                case ARB:
                case BASE:
                case SOLANA:
                    walletCoinRecService.insertByFlatChain(chainType, address, null);
                    break;
                default:
                    return R.fail("不支持的区块链类型: " + chainType);
            }

            log.info("钱包余额扫描完成: address={}, chainType={}", address, chainType);
            return R.ok("余额扫描完成，已新增记录");

        } catch (Exception e) {
            log.error("扫描钱包余额失败: address={}, chainType={}, error={}", address, chainType, e.getMessage(), e);
            return R.fail("扫描失败: " + e.getMessage());
        }
    }

    /**
     * 批量扫描多个地址的余额并新增记录
     *
     * @param addresses 钱包地址列表（逗号分隔）
     * @param chainType 区块链类型
     * @return 扫描结果
     */
    @Log(title = "批量扫描钱包余额", businessType = BusinessType.INSERT)
    @PostMapping("/scan/batch")
    public R<String> batchScanWalletBalance(@RequestParam @NotEmpty(message = "钱包地址列表不能为空") String addresses,
                                            @RequestParam @NotNull(message = "区块链类型不能为空") ChainType chainType) {
        try {
            // 解析地址列表
            String[] addressArray = addresses.split(",");
            if (addressArray.length == 0) {
                return R.fail("地址列表不能为空");
            }

            log.info("开始批量扫描钱包余额: addresses={}, chainType={}", Arrays.toString(addressArray), chainType);

            int successCount = 0;
            int failureCount = 0;
            StringBuilder errorMessages = new StringBuilder();

            for (String address : addressArray) {
                String trimmedAddress = address.trim();
                if (trimmedAddress.isEmpty()) {
                    continue;
                }

                try {
                    // 根据区块链类型调用对应的余额扫描服务
                    switch (chainType) {
                        case TRON:
                        case BSC:
                        case ARB:
                        case BASE:
                        case SOLANA:
                            walletCoinRecService.insertByFlatChain(chainType, trimmedAddress, null);
                            break;
                        default:
                            throw new IllegalArgumentException("不支持的区块链类型: " + chainType);
                    }
                    successCount++;
                    log.debug("地址扫描成功: {}", trimmedAddress);

                } catch (Exception e) {
                    failureCount++;
                    String errorMsg = String.format("地址 %s 扫描失败: %s", trimmedAddress, e.getMessage());
                    log.error(errorMsg, e);
                    if (!errorMessages.isEmpty()) {
                        errorMessages.append("; ");
                    }
                    errorMessages.append(errorMsg);
                }
            }

            String resultMessage = String.format("批量扫描完成，成功: %d, 失败: %d", successCount, failureCount);
            log.info("批量钱包余额扫描完成: {}, chainType={}", resultMessage, chainType);

            if (failureCount > 0) {
                return R.ok(resultMessage + "。失败详情: " + errorMessages);
            } else {
                return R.ok(resultMessage);
            }

        } catch (Exception e) {
            log.error("批量扫描钱包余额失败: addresses={}, chainType={}, error={}", addresses, chainType, e.getMessage(), e);
            return R.fail("批量扫描失败: " + e.getMessage());
        }
    }
}
