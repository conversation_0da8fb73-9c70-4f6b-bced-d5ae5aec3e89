package org.dromara.wallet.domain;

import org.dromara.common.mybatis.core.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.math.BigDecimal;

import java.io.Serial;

/**
 * ARB区块高度交易明细对象 meta_arb_transactions
 *
 * <AUTHOR>
 * @date 2025-06-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("meta_arb_transactions")
public class MetaArbTransaction extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * ID标识
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 交易hash
     */
    private String txid;

    /**
     * 交易区块高度
     */
    private Long blockheight;

    /**
     * 钱包转入地址
     */
    private String address;

    /**
     * 钱包转出地址
     */
    private String fromaddress;

    /**
     * 合约
     */
    private String contract;

    /**
     * 交易金额
     */
    private BigDecimal amount;

    /**
     * 交易手续费
     */
    private BigDecimal fee;

    /**
     * 时间戳
     */
    private Long timestamp;

    /**
     * 交易类型(collect-归集, send-转出, receive-转入, receive_other-归集钱包地址接收转账)
     */
    private String type;

    /**
     * 后端服务是否已处理(1-已处理,0-未处理,2-不处理)
     */
    private Integer issync;

    /**
     * 备注
     */
    private String remark;


}
