package org.dromara.wallet.domain;

import org.dromara.common.tenant.core.TenantEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * EVM兼容链客户热钱包地址信息对象 meta_bep20_cstaddressinfo
 * 支持所有EVM兼容链：BSC、ARB (Arbitrum)、BASE等
 * 由于EVM链使用相同的地址格式，可以共用同一个地址表
 *
 * <AUTHOR>
 * @date 2025-06-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("meta_bep20_cstaddressinfo")
public class MetaBep20Cstaddressinfo extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * ID标识
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 客户ID
     */
    private Long cstId;

    /**
     * 客户钱包地址
     */
    private String cstAddress;

    /**
     * 客户钱包私钥信息
     */
//    @TableField(value = "cst_trc20private")
    private String cstTrc20private;

    /**
     * 客户钱包16进制地址(暂未使用)
     */
    private String cstHexaddress;

    /**
     * 备注
     */
    private String remark;


}
