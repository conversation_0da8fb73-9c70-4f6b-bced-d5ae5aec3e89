package org.dromara.wallet.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.tenant.core.TenantEntity;

import java.io.Serial;

/**
 * Solana客户热钱包信息对象 meta_solana_cstaddressinfo
 *
 * <AUTHOR>
 * @date 2025-04-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("meta_solana_cstaddressinfo")
public class MetaSolanaCstaddressinfo extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * ID标识
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 客户ID
     */
    private Long cstId;

    /**
     * 客户钱包地址
     */
    private String cstAddress;

    /**
     * 客户钱包私钥信息
     */
    private String cstPrivate;

    /**
     * 客户USDT钱包地址
     */
    private String cstUsdtAddress;

    /**
     * 客户USDC钱包地址
     */
    private String cstUsdcAddress;

    /**
     * 渠道
     */
    private String tenantId;


}
