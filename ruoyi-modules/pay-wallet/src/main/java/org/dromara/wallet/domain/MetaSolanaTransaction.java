package org.dromara.wallet.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.io.Serial;
import java.math.BigDecimal;

/**
 * Solana区块高度交易明细对象 meta_solana_transactions
 *
 * <AUTHOR>
 * @date 2025-04-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("meta_solana_transactions")
public class MetaSolanaTransaction extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * ID标识
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 交易hash
     */
    private String txid;

    /**
     * 交易区块高度
     */
    private Long blockheight;

    /**
     * 钱包转入地址
     */
    private String address;

    /**
     * 钱包转出地址
     */
    private String fromaddress;

    /**
     * 合约
     */
    private String contract;

    /**
     * 交易金额
     */
    private BigDecimal amount;

    /**
     * 交易手续费
     */
    private BigDecimal fee;

    /**
     * 时间戳
     */
    private Long timestamp;

    /**
     * 交易类型(collect-归集, send-转出, receive-转入, receive_other-归集钱包接收转账)
     */
    private String type;

    /**
     * 后端服务是否已处理(1-已处理,0-未处理,2-不处理)
     */
    private Integer issync;


}
