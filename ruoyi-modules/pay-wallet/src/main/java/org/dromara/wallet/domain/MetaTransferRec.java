package org.dromara.wallet.domain;

import org.dromara.common.tenant.core.TenantEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.io.Serial;

/**
 * 转账记录对象 meta_transfer_rec
 *
 * <AUTHOR>
 * @date 2025-07-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("meta_transfer_rec")
public class MetaTransferRec extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 交易哈希
     */
    private String transactionHash;

    /**
     * 区块链名称(TRON/BSC/ETH/ARB/BASE/SOLANA)
     */
    private String chainName;

    /**
     * 原始交易表名
     */
    private String originalTable;

    /**
     * 原始交易记录ID
     */
    private Long originalId;

    /**
     * 确认状态(0-待确认,1-确认中,2-已确认,3-确认失败,4-超时)
     */
    private Integer confirmationStatus;

    /**
     * 所需确认数
     */
    private Integer requiredConfirmations;

    /**
     * 实际确认数
     */
    private Integer actualConfirmations;

    /**
     * 交易所在区块高度
     */
    private Long blockHeight;

    /**
     * 区块哈希
     */
    private String blockHash;

    /**
     * 确认开始时间
     */
    private Date startTime;

    /**
     * 确认结束时间
     */
    private Date endTime;

    /**
     * 确认耗时(毫秒)
     */
    private Long confirmationTimeMs;

    /**
     * 重试次数
     */
    private Integer retryCount;

    /**
     * 最大重试次数
     */
    private Integer maxRetries;

    /**
     * 下次重试时间
     */
    private Date nextRetryTime;

    /**
     * 超时时间(秒)
     */
    private Integer timeoutSeconds;

    /**
     * 检查间隔(秒)
     */
    private Integer checkIntervalSeconds;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 额外信息(链特定数据)
     */
    private String extraInfo;

    /**
     * 请求ID
     */
    private String requestId;

    /**
     * 转账类型(native/token/collection)
     */
    private String transferType;

    /**
     * 事件优先级
     */
    private Integer eventPriority;

    /**
     * 备注
     */
    private String remark;


}
