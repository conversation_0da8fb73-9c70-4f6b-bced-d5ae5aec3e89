package org.dromara.wallet.domain;

import org.dromara.common.tenant.core.TenantEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * TRON客户热钱包地址信息对象 meta_trc20_cstaddressinfo
 *
 * <AUTHOR>
 * @date 2025-06-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("meta_trc20_cstaddressinfo")
public class MetaTrc20Cstaddressinfo extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * ID标识
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 客户ID
     */
    private Long cstId;

    /**
     * 客户钱包地址
     */
    private String cstAddress;

    /**
     * 客户钱包私钥信息
     */
    private String cstTrc20private;

    /**
     * 客户钱包16进制地址(暂未使用)
     */
    private String cstHexaddress;

    /**
     * 备注
     */
    private String remark;


}
