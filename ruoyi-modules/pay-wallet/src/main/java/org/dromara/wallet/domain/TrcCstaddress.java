package org.dromara.wallet.domain;

import org.dromara.common.tenant.core.TenantEntity;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serial;

/**
 * TRON钱包地址实体类
 *
 * <AUTHOR>
 * @date 2025/1/27
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("meta_trc20_cstaddressinfo")
@Accessors(chain = true)
public class TrcCstaddress extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * ID标识
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 客户ID
     */
    private Long cstId;

    /**
     * 客户钱包地址
     */
    private String cstAddress;

    /**
     * 客户钱包私钥信息
     */
    private String cstTrc20private;

    /**
     * 客户钱包16进制地址(暂未使用)
     */
    private String cstHexaddress;

    /**
     * 备注
     */
    private String remark;

    /**
     * 二维码
     */
    @TableField(exist = false)
    private String qrcode;

    /**
     * md5校验
     */
    @TableField(exist = false)
    private String md5sum;

    /**
     * 合约地址
     */
    @TableField(exist = false)
    private String contractAddress;

    /**
     * 币种类型
     */
    @TableField(exist = false)
    private String coinType;

}
