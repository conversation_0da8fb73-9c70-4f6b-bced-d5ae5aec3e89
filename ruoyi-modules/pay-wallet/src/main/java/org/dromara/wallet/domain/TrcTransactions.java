package org.dromara.wallet.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.tenant.core.TenantEntity;

import java.io.Serial;
import java.math.BigDecimal;

/**
 * TRON链交易记录
 *
 * <AUTHOR>
 * @date 2023/11/23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("meta_trc20_transactions")
public class TrcTransactions extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 交易ID
     */
    private String txid;

    /**
     * 区块高度
     */
    private Long blockHeight;

    /**
     * 接收地址
     */
    private String address;

    /**
     * 发送地址
     */
    private String fromaddress;

    /**
     * 合约地址
     */
    private String contract;

    /**
     * 交易金额
     */
    private BigDecimal amount;

    /**
     * 手续费
     */
    private BigDecimal fee;

    /**
     * 时间戳
     */
    private int timestamp;

    /**
     * 交易类型
     */
    private String type;

    /**
     * 是否同步
     */
    private int isSync;
}
