package org.dromara.wallet.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.tenant.core.TenantEntity;

import java.io.Serial;
import java.math.BigDecimal;
import java.math.BigInteger;

/**
 * 多链用户钱包代币余额记录表
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("meta_wallet_coin_rec")
public class WalletCoinRec extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * ID标识(雪花算法)
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 钱包地址
     */
    private String walletAddress;

    /**
     * 代币合约地址 (原生代币固定值，如TRX为"_TRX_NATIVE_")
     */
    private String tokenAddress;

    /**
     * 代币符号 (例如: TRX, USDT, USDC)
     */
    private String tokenSymbol;

    /**
     * 可读余额
     */
    private BigDecimal balance;

    /**
     * 代币小数位数
     */
    private Integer decimals;

    /**
     * 链类型 (例如: TRON, ETH, BSC)
     */
    private String chainType;

    /**
     * 备注
     */
    private String remark;
}
