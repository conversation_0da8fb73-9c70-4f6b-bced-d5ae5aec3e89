package org.dromara.wallet.domain;

import org.dromara.common.tenant.core.TenantEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 提款处理记录对象 withdraw_process_rec
 *
 * <AUTHOR>
 * @date 2025-07-17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("withdraw_process_rec")
public class WithdrawProcessRec extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * ID标识
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 外部关联id（订单id）
     */
    private Long orderId;

    /**
     * 区块链交易哈希
     */
    private String txid;

    /**
     * 状态（0:转账中，1:成功，2:失败）
     */
    private Integer status;

    /**
     * 备注
     */
    private String remark;


}
