package org.dromara.wallet.domain.bo;

import org.dromara.wallet.domain.MetaBep20Cstaddressinfo;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

/**
 * ETH客户热钱包地址信息业务对象 meta_bep20_cstaddressinfo
 *
 * <AUTHOR>
 * @date 2025-06-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = MetaBep20Cstaddressinfo.class, reverseConvertGenerate = false)
public class MetaBep20CstaddressinfoBo extends BaseEntity {

    /**
     * ID标识
     */
    @NotNull(message = "ID标识不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 客户ID
     */
    private Long cstId;

    /**
     * 客户钱包地址
     */
    private String cstAddress;

    /**
     * 客户钱包私钥信息
     */
    private String cstTrc20private;

    /**
     * 客户钱包16进制地址(暂未使用)
     */
    private String cstHexaddress;

    /**
     * 备注
     */
    private String remark;


}
