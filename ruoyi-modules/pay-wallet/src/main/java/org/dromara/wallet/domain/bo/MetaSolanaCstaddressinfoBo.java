package org.dromara.wallet.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.wallet.domain.MetaSolanaCstaddressinfo;

/**
 * Solana客户热钱包信息业务对象 meta_solana_cstaddressinfo
 *
 * <AUTHOR>
 * @date 2025-04-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = MetaSolanaCstaddressinfo.class, reverseConvertGenerate = false)
public class MetaSolanaCstaddressinfoBo extends BaseEntity {

    /**
     * ID标识
     */
    @NotNull(message = "ID标识不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 客户ID
     */
    private Long cstId;

    /**
     * 客户钱包地址
     */
    private String cstAddress;

    /**
     * 客户钱包私钥信息
     */
    private String cstPrivate;

    /**
     * 客户USDT钱包地址
     */
    private String cstUsdtAddress;

    /**
     * 客户USDC钱包地址
     */
    private String cstUsdcAddress;

    /**
     * 渠道
     */
    @NotBlank(message = "渠道不能为空", groups = { AddGroup.class, EditGroup.class })
    private String tenantId;


}
