package org.dromara.wallet.domain.bo;

import org.dromara.wallet.domain.MetaTransferRec;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 转账记录业务对象 meta_transfer_rec
 *
 * <AUTHOR>
 * @date 2025-07-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = MetaTransferRec.class, reverseConvertGenerate = false)
public class MetaTransferRecBo extends BaseEntity {

    /**
     * 主键ID
     */
    @NotNull(message = "主键ID不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 交易哈希
     */
    @NotBlank(message = "交易哈希不能为空", groups = { AddGroup.class, EditGroup.class })
    private String transactionHash;

    /**
     * 区块链名称(TRON/BSC/ETH/ARB/BASE/SOLANA)
     */
    @NotBlank(message = "区块链名称(TRON/BSC/ETH/ARB/BASE/SOLANA)不能为空", groups = { AddGroup.class, EditGroup.class })
    private String chainName;

    /**
     * 原始交易表名
     */
    private String originalTable;

    /**
     * 原始交易记录ID
     */
    private Long originalId;

    /**
     * 确认状态(0-待确认,1-确认中,2-已确认,3-确认失败,4-超时)
     */
    @NotNull(message = "确认状态(0-待确认,1-确认中,2-已确认,3-确认失败,4-超时)不能为空", groups = { AddGroup.class, EditGroup.class })
    private Integer confirmationStatus;

    /**
     * 所需确认数
     */
    @NotNull(message = "所需确认数不能为空", groups = { AddGroup.class, EditGroup.class })
    private Integer requiredConfirmations;

    /**
     * 实际确认数
     */
    private Integer actualConfirmations;

    /**
     * 交易所在区块高度
     */
    private Long blockHeight;

    /**
     * 区块哈希
     */
    private String blockHash;

    /**
     * 确认开始时间
     */
    @NotNull(message = "确认开始时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date startTime;

    /**
     * 确认结束时间
     */
    private Date endTime;

    /**
     * 确认耗时(毫秒)
     */
    private Long confirmationTimeMs;

    /**
     * 重试次数
     */
    @NotNull(message = "重试次数不能为空", groups = { AddGroup.class, EditGroup.class })
    private Integer retryCount;

    /**
     * 最大重试次数
     */
    @NotNull(message = "最大重试次数不能为空", groups = { AddGroup.class, EditGroup.class })
    private Integer maxRetries;

    /**
     * 下次重试时间
     */
    private Date nextRetryTime;

    /**
     * 超时时间(秒)
     */
    @NotNull(message = "超时时间(秒)不能为空", groups = { AddGroup.class, EditGroup.class })
    private Integer timeoutSeconds;

    /**
     * 检查间隔(秒)
     */
    @NotNull(message = "检查间隔(秒)不能为空", groups = { AddGroup.class, EditGroup.class })
    private Integer checkIntervalSeconds;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 额外信息(链特定数据)
     */
    private String extraInfo;

    /**
     * 请求ID
     */
    private String requestId;

    /**
     * 转账类型(native/token/collection)
     */
    private String transferType;

    /**
     * 事件优先级
     */
    @NotNull(message = "事件优先级不能为空", groups = { AddGroup.class, EditGroup.class })
    private Integer eventPriority;

    /**
     * 备注
     */
    private String remark;


}
