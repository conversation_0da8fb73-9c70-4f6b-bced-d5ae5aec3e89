package org.dromara.wallet.domain.bo;

import org.dromara.wallet.domain.MetaTrc20Transaction;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;
import java.math.BigDecimal;

/**
 * TRON区块高度交易明细业务对象 meta_trc20_transactions
 *
 * <AUTHOR>
 * @date 2025-06-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = MetaTrc20Transaction.class, reverseConvertGenerate = false)
public class MetaTrc20TransactionBo extends BaseEntity {

    /**
     * ID标识
     */
    @NotNull(message = "ID标识不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 交易hash
     */
    private String txid;

    /**
     * 交易区块高度
     */
    private Long blockheight;

    /**
     * 钱包转入地址
     */
    private String address;

    /**
     * 钱包转出地址
     */
    private String fromaddress;

    /**
     * 合约
     */
    private String contract;

    /**
     * 交易金额
     */
    private BigDecimal amount;

    /**
     * 交易手续费
     */
    private BigDecimal fee;

    /**
     * 时间戳
     */
    private Long timestamp;

    /**
     * 交易类型(collect-归集, send-转出, receive-转入, receive_other-归集钱包地址接收转账)
     */
    private String type;

    /**
     * 后端服务是否已处理(1-已处理,0-未处理,2-不处理)
     */
    private Long issync;

    /**
     * 备注
     */
    private String remark;


}
