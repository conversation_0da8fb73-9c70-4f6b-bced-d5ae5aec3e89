package org.dromara.wallet.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.wallet.domain.TrcCstaddress;

/**
 * TRON钱包地址业务对象 meta_trc20_cstaddressinfo
 *
 * <AUTHOR>
 * @date 2025/1/27
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = TrcCstaddress.class, reverseConvertGenerate = false)
public class TrcCstaddressBo extends BaseEntity {

    /**
     * ID标识
     */
    @NotNull(message = "ID标识不能为空", groups = {EditGroup.class})
    private Long id;

    /**
     * 客户ID
     */
    @NotNull(message = "客户ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long cstId;

    /**
     * 客户钱包地址
     */
    @NotBlank(message = "客户钱包地址不能为空", groups = {AddGroup.class, EditGroup.class})
    private String cstAddress;

    /**
     * 客户钱包私钥信息
     */
    @NotBlank(message = "客户钱包私钥信息不能为空", groups = {AddGroup.class, EditGroup.class})
    private String cstTrc20private;

    /**
     * 客户钱包16进制地址
     */
    private String cstHexaddress;

    /**
     * 二维码
     */
    private String qrcode;

    /**
     * md5校验
     */
    private String md5sum;

    /**
     * 钱包地址
     */
    private String walletAddress;

    /**
     * 合约地址
     */
    private String contractAddress;

    /**
     * 币种类型
     */
    private String coinType;
}
