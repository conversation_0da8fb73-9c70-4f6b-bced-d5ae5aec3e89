package org.dromara.wallet.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.wallet.domain.TrcTransactions;

import java.math.BigDecimal;

/**
 * TRON链交易记录业务对象
 *
 * <AUTHOR>
 * @date 2025/1/27
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = TrcTransactions.class, reverseConvertGenerate = false)
public class TrcTransactionsBo extends BaseEntity {

    /**
     * 主键ID
     */
    @NotNull(message = "主键ID不能为空", groups = {EditGroup.class})
    private Long id;

    /**
     * 交易ID
     */
    @NotBlank(message = "交易ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private String txid;

    /**
     * 区块高度
     */
    private Long blockHeight;

    /**
     * 接收地址
     */
    @NotBlank(message = "接收地址不能为空", groups = {AddGroup.class, EditGroup.class})
    private String address;

    /**
     * 发送地址
     */
    private String fromaddress;

    /**
     * 合约地址
     */
    private String contract;

    /**
     * 交易金额
     */
    @NotNull(message = "交易金额不能为空", groups = {AddGroup.class, EditGroup.class})
    private BigDecimal amount;

    /**
     * 手续费
     */
    private BigDecimal fee;

    /**
     * 时间戳
     */
    private int timestamp;

    /**
     * 交易类型
     */
    private String type;

    /**
     * 是否同步
     */
    private int isSync;
}
