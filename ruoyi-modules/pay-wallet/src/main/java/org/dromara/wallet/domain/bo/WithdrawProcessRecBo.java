package org.dromara.wallet.domain.bo;

import org.dromara.wallet.domain.WithdrawProcessRec;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

import java.math.BigDecimal;

/**
 * 提款处理记录业务对象 withdraw_process_rec
 *
 * <AUTHOR>
 * @date 2025-07-17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = WithdrawProcessRec.class, reverseConvertGenerate = false)
public class WithdrawProcessRecBo extends BaseEntity {

    /**
     * ID标识
     */
    @NotNull(message = "ID标识不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 外部关联id（订单id）
     */
    @NotNull(message = "外部关联id（订单id）不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long orderId;

    /**
     * 区块链交易哈希
     */
    private String txid;

    /**
     * 状态（0:转账中，1:成功，2:失败）
     */
    @NotNull(message = "状态（0:转账中，1:成功，2:失败）不能为空", groups = { AddGroup.class, EditGroup.class })
    private Integer status;

    /**
     * 备注
     */
    private String remark;
    private BigDecimal amount;
    private String toAddress;
    private String tokenSymbol;
    private String chainName;


}
