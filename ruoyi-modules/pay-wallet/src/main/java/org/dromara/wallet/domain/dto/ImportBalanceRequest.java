package org.dromara.wallet.domain.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import lombok.Data;
import org.dromara.wallet.config.ChainType;

import java.math.BigDecimal;

/**
 * 手动入账请求DTO
 * 封装手动入账操作所需的所有参数
 *
 * <AUTHOR>
 */
@Data
public class ImportBalanceRequest {

    /**
     * 钱包地址
     */
    @NotBlank(message = "钱包地址不能为空")
    private String address;

    /**
     * 钱包私钥
     */
    @NotBlank(message = "钱包私钥不能为空")
    private String privateKey;

    /**
     * 入账金额（可读格式）
     */
    @NotNull(message = "入账金额不能为空")
    @Positive(message = "入账金额必须大于0")
    private BigDecimal amount;

    /**
     * 代币符号
     */
    @NotBlank(message = "代币符号不能为空")
    private String tokenSymbol;

    /**
     * 区块链类型
     */
    @NotNull(message = "区块链类型不能为空")
    private ChainType chainType;

    /**
     * 入账原因/备注
     */
    private String reason;

    /**
     * 操作员ID（可选，用于审计）
     */
    private Long operatorId;

    /**
     * 是否强制入账（跳过余额验证）
     */
    private boolean forceImport = false;

    /**
     * 验证请求参数
     */
    public void validate() {
        if (address == null || address.trim().isEmpty()) {
            throw new IllegalArgumentException("钱包地址不能为空");
        }

        if (privateKey == null || privateKey.trim().isEmpty()) {
            throw new IllegalArgumentException("钱包私钥不能为空");
        }

        if (amount == null || amount.compareTo(BigDecimal.ZERO) <= 0) {
            throw new IllegalArgumentException("入账金额必须大于0");
        }

        if (tokenSymbol == null || tokenSymbol.trim().isEmpty()) {
            throw new IllegalArgumentException("代币符号不能为空");
        }

        if (chainType == null) {
            throw new IllegalArgumentException("区块链类型不能为空");
        }
    }

    /**
     * 创建入账请求的构建器
     */
    public static ImportBalanceRequestBuilder builder() {
        return new ImportBalanceRequestBuilder();
    }

    /**
     * 入账请求构建器
     */
    public static class ImportBalanceRequestBuilder {
        private final ImportBalanceRequest request = new ImportBalanceRequest();

        public ImportBalanceRequestBuilder address(String address) {
            request.setAddress(address);
            return this;
        }

        public ImportBalanceRequestBuilder privateKey(String privateKey) {
            request.setPrivateKey(privateKey);
            return this;
        }

        public ImportBalanceRequestBuilder amount(BigDecimal amount) {
            request.setAmount(amount);
            return this;
        }

        public ImportBalanceRequestBuilder amount(String amount) {
            request.setAmount(new BigDecimal(amount));
            return this;
        }

        public ImportBalanceRequestBuilder tokenSymbol(String tokenSymbol) {
            request.setTokenSymbol(tokenSymbol);
            return this;
        }

        public ImportBalanceRequestBuilder chainType(ChainType chainType) {
            request.setChainType(chainType);
            return this;
        }

        public ImportBalanceRequestBuilder reason(String reason) {
            request.setReason(reason);
            return this;
        }

        public ImportBalanceRequestBuilder operatorId(Long operatorId) {
            request.setOperatorId(operatorId);
            return this;
        }

        public ImportBalanceRequestBuilder forceImport(boolean forceImport) {
            request.setForceImport(forceImport);
            return this;
        }

        public ImportBalanceRequest build() {
            request.validate();
            return request;
        }
    }
}
