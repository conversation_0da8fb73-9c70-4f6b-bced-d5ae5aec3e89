package org.dromara.wallet.domain.dto;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * 主钱包地址(归集钱包地址）
 *
 * <AUTHOR>
 * @date 2025/5/9 13:14
 **/
@Getter
@Setter
public class MetaMainAddress {

    /**
     * 商户id
     */
    private String tenantId;

    /**
     * 类型（BEP20/TRC20/SOL）
     */
    private String type;

    /**
     * 主钱包地址
     */
    private String address;

    /**
     * 归集阈值
     */
    private BigDecimal collectThreshold;


    /**
     * 告警阈值
     */
    private BigDecimal alarmThreshold;

}

