package org.dromara.wallet.domain.dto;

import lombok.Data;
import org.dromara.wallet.config.ChainType;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 钱包归集响应DTO
 * 简化版归集结果
 *
 * <AUTHOR>
 */
@Data
public class WalletCollectVo {

    /**
     * 归集操作是否成功
     */
    private boolean success;

    /**
     * 钱包地址
     */
    private String walletAddress;

    /**
     * 区块链类型
     */
    private ChainType chainType;

    /**
     * 代币符号
     */
    private String tokenSymbol;

    /**
     * 归集目标钱包地址
     */
    private String targetAddress;

    /**
     * 归集金额
     */
    private BigDecimal amount;

    /**
     * 交易哈希
     */
    private String transactionHash;

    /**
     * 手续费
     */
    private BigDecimal fee;

    /**
     * 处理时间
     */
    private LocalDateTime processTime;

    /**
     * 错误信息（如果失败）
     */
    private String errorMessage;

    /**
     * 创建成功响应
     */
    public static WalletCollectVo success(String walletAddress, ChainType chainType, String tokenSymbol,
                                          String targetAddress, BigDecimal amount, String transactionHash) {
        WalletCollectVo response = new WalletCollectVo();
        response.setSuccess(true);
        response.setWalletAddress(walletAddress);
        response.setChainType(chainType);
        response.setTokenSymbol(tokenSymbol);
        response.setTargetAddress(targetAddress);
        response.setAmount(amount);
        response.setTransactionHash(transactionHash);
        response.setProcessTime(LocalDateTime.now());
        return response;
    }

    /**
     * 创建失败响应
     */
    public static WalletCollectVo failure(String walletAddress, ChainType chainType, String tokenSymbol, String errorMessage) {
        WalletCollectVo response = new WalletCollectVo();
        response.setSuccess(false);
        response.setWalletAddress(walletAddress);
        response.setChainType(chainType);
        response.setTokenSymbol(tokenSymbol);
        response.setErrorMessage(errorMessage);
        response.setProcessTime(LocalDateTime.now());
        return response;
    }
}
