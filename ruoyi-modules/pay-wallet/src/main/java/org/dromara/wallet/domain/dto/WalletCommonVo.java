package org.dromara.wallet.domain.dto;

import lombok.Data;
import org.dromara.wallet.config.ChainType;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 钱包操作响应DTO
 * 统一封装钱包操作的响应结果
 *
 * <AUTHOR>
 */
@Data
public class WalletCommonVo {

    /**
     * 操作是否成功
     */
    private boolean success;

    /**
     * 交易哈希（如果有）
     */
    private String transactionHash;

    /**
     * 操作类型
     */
    private OperationType operationType;

    /**
     * 区块链类型
     */
    private ChainType chainType;

    /**
     * 代币符号
     */
    private String tokenSymbol;

    /**
     * 操作金额
     */
    private BigDecimal amount;

    /**
     * 发送方地址
     */
    private String fromAddress;

    /**
     * 接收方地址
     */
    private String toAddress;

    /**
     * 操作时间
     */
    private LocalDateTime operationTime;

    /**
     * 错误信息（如果操作失败）
     */
    private String errorMessage;

    /**
     * 手续费（如果适用）
     */
    private BigDecimal fee;

    /**
     * 操作备注
     */
    private String memo;

    /**
     * 操作类型枚举
     */
    public enum OperationType {
        /**
         * 转账
         */
        TRANSFER("转账"),

        /**
         * 归集
         */
        COLLECT("归集"),

        /**
         * 手动入账
         */
        IMPORT_BALANCE("手动入账");

        private final String description;

        OperationType(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 创建成功响应
     */
    public static WalletCommonVo success(OperationType operationType,
                                         ChainType chainType,
                                         String tokenSymbol,
                                         BigDecimal amount) {
        WalletCommonVo response = new WalletCommonVo();
        response.setSuccess(true);
        response.setOperationType(operationType);
        response.setChainType(chainType);
        response.setTokenSymbol(tokenSymbol);
        response.setAmount(amount);
        response.setOperationTime(LocalDateTime.now());
        return response;
    }

    /**
     * 创建失败响应
     */
    public static WalletCommonVo failure(OperationType operationType,
                                         String errorMessage) {
        WalletCommonVo response = new WalletCommonVo();
        response.setSuccess(false);
        response.setOperationType(operationType);
        response.setErrorMessage(errorMessage);
        response.setOperationTime(LocalDateTime.now());
        return response;
    }

    /**
     * 设置交易信息
     */
    public WalletCommonVo withTransaction(String transactionHash,
                                          String fromAddress,
                                          String toAddress) {
        this.transactionHash = transactionHash;
        this.fromAddress = fromAddress;
        this.toAddress = toAddress;
        return this;
    }

    /**
     * 设置手续费
     */
    public WalletCommonVo withFee(BigDecimal fee) {
        this.fee = fee;
        return this;
    }

    /**
     * 设置备注
     */
    public WalletCommonVo withMemo(String memo) {
        this.memo = memo;
        return this;
    }
}
