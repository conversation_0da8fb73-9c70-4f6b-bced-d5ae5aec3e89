package org.dromara.wallet.domain.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import lombok.Data;
import org.dromara.wallet.config.ChainType;

import java.math.BigDecimal;
import java.util.List;

/**
 * 钱包转账请求DTO
 * 封装转账操作所需的所有参数
 *
 * <AUTHOR>
 */
@Data
public class WalletWithdrawBo {

    /**
     * 外部关联id（订单id）
     */
    private String requestId;

    /**
     * 接收方地址
     */
    @NotEmpty(message = "接收方地址不能为空")
    private String toAddress;

    /**
     * 转账金额（可读格式）
     */
    @NotNull(message = "转账金额不能为空")
    @Positive(message = "转账金额必须大于0")
    private BigDecimal amount;

    /**
     * 代币符号
     */
    @NotBlank(message = "代币符号不能为空")
    private String tokenSymbol;

    /**
     * 区块链类型
     */
    @NotNull(message = "区块链类型不能为空")
    private ChainType chainType;

    /**
     * 转账备注（可选）
     */
    private String memo;

}
