package org.dromara.wallet.domain.vo;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;


/**
 * 提款处理记录视图对象 withdraw_process_rec
 *
 * <AUTHOR>
 * @date 2025-07-17
 */
@Data
@ExcelIgnoreUnannotated
public class CapitalWalletVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 钱包地址
     */
    private String walletAddress;

    /**
     * 该钱包在所有链上的币种资产列表 (扁平化结构)
     */
    private List<CoinVo> coins = new ArrayList<>();

    /**
     * CoinVo (币种视图对象)
     */
    public record CoinVo(
        /*
         * 代币符号 (e.g., "USDT", "BNB")
         */
        String symbol,

        /*
         * 代币数量
         */
        BigDecimal balance
    ) {
    }

}
