package org.dromara.wallet.domain.vo;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.wallet.domain.MetaSolanaTransaction;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;


/**
 * Solana区块高度交易明细视图对象 meta_solana_transactions
 *
 * <AUTHOR>
 * @date 2025-04-15
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = MetaSolanaTransaction.class)
public class MetaSolanaTransactionVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * ID标识
     */
    @ExcelProperty(value = "ID标识")
    private Long id;

    /**
     * 交易hash
     */
    @ExcelProperty(value = "交易hash")
    private String txid;

    /**
     * 交易区块高度
     */
    @ExcelProperty(value = "交易区块高度")
    private Long blockheight;

    /**
     * 钱包转入地址
     */
    @ExcelProperty(value = "钱包转入地址")
    private String address;

    /**
     * 钱包转出地址
     */
    @ExcelProperty(value = "钱包转出地址")
    private String fromaddress;

    /**
     * 合约
     */
    @ExcelProperty(value = "合约")
    private String contract;

    /**
     * 交易金额
     */
    @ExcelProperty(value = "交易金额")
    private BigDecimal amount;

    /**
     * 交易手续费
     */
    @ExcelProperty(value = "交易手续费")
    private BigDecimal fee;

    /**
     * 时间戳
     */
    @ExcelProperty(value = "时间戳")
    private Long timestamp;

    /**
     * 交易类型(collect-归集, send-转出, receive-转入, receive_other-归集钱包接收转账)
     */
    @ExcelProperty(value = "交易类型(collect-归集, send-转出, receive-转入, receive_other-归集钱包接收转账)")
    private String type;

    /**
     * 时间
     */
    @ExcelProperty(value = "时间")
    private Date time;

    /**
     * 币种
     */
    @ExcelProperty(value = "币种")
    private String coinType;

    /**
     * 用户id
     */
    @ExcelProperty(value = "用户id")
    private Long cstId;

    /**
     * 后端服务是否已处理(1-已处理,0-未处理,2-不处理)
     */
    @ExcelProperty(value = "后端服务是否已处理(1-已处理,0-未处理,2-不处理)")
    private Integer issync;


}
