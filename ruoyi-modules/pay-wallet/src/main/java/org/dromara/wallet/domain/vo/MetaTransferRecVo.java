package org.dromara.wallet.domain.vo;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.dromara.wallet.domain.MetaTransferRec;
import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 转账记录视图对象 meta_transfer_rec
 *
 * <AUTHOR>
 * @date 2025-07-19
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = MetaTransferRec.class)
public class MetaTransferRecVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ExcelProperty(value = "主键ID")
    private Long id;

    /**
     * 交易哈希
     */
    @ExcelProperty(value = "交易哈希")
    private String transactionHash;

    /**
     * 区块链名称(TRON/BSC/ETH/ARB/BASE/SOLANA)
     */
    @ExcelProperty(value = "区块链名称(TRON/BSC/ETH/ARB/BASE/SOLANA)")
    private String chainName;

    /**
     * 原始交易表名
     */
    @ExcelProperty(value = "原始交易表名")
    private String originalTable;

    /**
     * 原始交易记录ID
     */
    @ExcelProperty(value = "原始交易记录ID")
    private Long originalId;

    /**
     * 确认状态(0-待确认,1-确认中,2-已确认,3-确认失败,4-超时)
     */
    @ExcelProperty(value = "确认状态(0-待确认,1-确认中,2-已确认,3-确认失败,4-超时)")
    private Integer confirmationStatus;

    /**
     * 所需确认数
     */
    @ExcelProperty(value = "所需确认数")
    private Integer requiredConfirmations;

    /**
     * 实际确认数
     */
    @ExcelProperty(value = "实际确认数")
    private Integer actualConfirmations;

    /**
     * 交易所在区块高度
     */
    @ExcelProperty(value = "交易所在区块高度")
    private Long blockHeight;

    /**
     * 区块哈希
     */
    @ExcelProperty(value = "区块哈希")
    private String blockHash;

    /**
     * 确认开始时间
     */
    @ExcelProperty(value = "确认开始时间")
    private Date startTime;

    /**
     * 确认结束时间
     */
    @ExcelProperty(value = "确认结束时间")
    private Date endTime;

    /**
     * 确认耗时(毫秒)
     */
    @ExcelProperty(value = "确认耗时(毫秒)")
    private Long confirmationTimeMs;

    /**
     * 重试次数
     */
    @ExcelProperty(value = "重试次数")
    private Integer retryCount;

    /**
     * 最大重试次数
     */
    @ExcelProperty(value = "最大重试次数")
    private Integer maxRetries;

    /**
     * 下次重试时间
     */
    @ExcelProperty(value = "下次重试时间")
    private Date nextRetryTime;

    /**
     * 超时时间(秒)
     */
    @ExcelProperty(value = "超时时间(秒)")
    private Integer timeoutSeconds;

    /**
     * 检查间隔(秒)
     */
    @ExcelProperty(value = "检查间隔(秒)")
    private Integer checkIntervalSeconds;

    /**
     * 错误信息
     */
    @ExcelProperty(value = "错误信息")
    private String errorMessage;

    /**
     * 额外信息(链特定数据)
     */
    @ExcelProperty(value = "额外信息(链特定数据)")
    private String extraInfo;

    /**
     * 请求ID
     */
    @ExcelProperty(value = "请求ID")
    private String requestId;

    /**
     * 转账类型(native/token/collection)
     */
    @ExcelProperty(value = "转账类型(native/token/collection)")
    private String transferType;

    /**
     * 事件优先级
     */
    @ExcelProperty(value = "事件优先级")
    private Integer eventPriority;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;


}
