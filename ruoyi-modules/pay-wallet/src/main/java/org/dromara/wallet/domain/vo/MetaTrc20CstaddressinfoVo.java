package org.dromara.wallet.domain.vo;

import org.dromara.wallet.domain.MetaTrc20Cstaddressinfo;
import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * TRON客户热钱包地址信息视图对象 meta_trc20_cstaddressinfo
 *
 * <AUTHOR>
 * @date 2025-06-25
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = MetaTrc20Cstaddressinfo.class)
public class MetaTrc20CstaddressinfoVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * ID标识
     */
    @ExcelProperty(value = "ID标识")
    private Long id;

    /**
     * 客户ID
     */
    @ExcelProperty(value = "客户ID")
    private Long cstId;

    /**
     * 客户钱包地址
     */
    @ExcelProperty(value = "客户钱包地址")
    private String cstAddress;

    /**
     * 客户钱包私钥信息
     */
    @ExcelProperty(value = "客户钱包私钥信息")
    private String cstTrc20private;

    /**
     * 客户钱包16进制地址(暂未使用)
     */
    @ExcelProperty(value = "客户钱包16进制地址(暂未使用)")
    private String cstHexaddress;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;


}
