package org.dromara.wallet.domain.vo;

import lombok.Data;
import lombok.Builder;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * Solana监控状态响应VO
 * 
 * <AUTHOR>
 */
@Data
@Builder
public class MonitorStatusVo {

    /**
     * 连接状态
     */
    private Boolean connected;

    /**
     * 当前连接ID
     */
    private Long connectionId;

    /**
     * 连接建立时间
     */
    private LocalDateTime connectionTime;

    /**
     * 监控是否启用
     */
    private Boolean monitorEnabled;

    /**
     * 总监控地址数量
     */
    private Integer totalAddresses;

    /**
     * 已成功订阅地址数量
     */
    private Integer subscribedAddresses;

    /**
     * 重连次数
     */
    private Integer reconnectAttempts;

    /**
     * 是否正在重连
     */
    private Boolean isReconnecting;

    /**
     * 按币种统计的订阅数量
     */
    private Map<String, Integer> coinTypeStats;

    /**
     * 交易统计信息
     */
    private TransactionStatsVo transactionStats;

    /**
     * 最后一次订阅统计信息
     */
    private SubscriptionStatsVo lastSubscriptionStats;

    /**
     * 交易统计VO
     */
    @Data
    @Builder
    public static class TransactionStatsVo {
        /**
         * 总接收交易数
         */
        private Long totalTransactions;

        /**
         * 今日接收交易数
         */
        private Long todayTransactions;

        /**
         * 最后接收交易时间
         */
        private LocalDateTime lastTransactionTime;
    }

    /**
     * 订阅统计VO
     */
    @Data
    @Builder
    public static class SubscriptionStatsVo {
        /**
         * 连接ID
         */
        private String connectionId;

        /**
         * 总钱包数
         */
        private Integer totalWallets;

        /**
         * 总地址数
         */
        private Integer totalAddresses;

        /**
         * 成功订阅数
         */
        private Integer successfulSubscriptions;

        /**
         * 失败订阅数
         */
        private Integer failedSubscriptions;

        /**
         * 开始时间
         */
        private LocalDateTime startTime;

        /**
         * 结束时间
         */
        private LocalDateTime endTime;

        /**
         * 按币种统计
         */
        private Map<String, Integer> coinTypeStats;
    }
}
