package org.dromara.wallet.domain.vo;

import lombok.Data;
import lombok.Builder;

/**
 * Solana监控摘要响应VO
 * 
 * <AUTHOR>
 */
@Data
@Builder
public class MonitorSummaryVo {

    /**
     * 连接状态
     */
    private Boolean connected;

    /**
     * 监控是否启用
     */
    private Boolean enabled;

    /**
     * 总监控地址数量
     */
    private Integer totalAddresses;

    /**
     * 已成功订阅地址数量
     */
    private Integer subscribedAddresses;

    /**
     * 重连次数
     */
    private Integer reconnectAttempts;

    /**
     * 总交易数
     */
    private Long totalTransactions;

    /**
     * 今日交易数
     */
    private Long todayTransactions;
}
