package org.dromara.wallet.domain.vo;

import lombok.Data;
import lombok.Builder;
import java.time.LocalDateTime;

/**
 * Solana订阅信息响应VO
 * 
 * <AUTHOR>
 */
@Data
@Builder
public class SubscriptionInfoVo {

    /**
     * 订阅ID
     */
    private String subscriptionId;

    /**
     * 监控地址
     */
    private String address;

    /**
     * 币种类型
     */
    private String coinType;

    /**
     * 钱包ID
     */
    private Long walletId;

    /**
     * 订阅状态
     */
    private String status;

    /**
     * 订阅时间
     */
    private LocalDateTime subscriptionTime;

    /**
     * 连接ID
     */
    private Long connectionId;

    /**
     * 是否活跃
     */
    private Boolean active;

    /**
     * 最后活动时间
     */
    private LocalDateTime lastActivityTime;
}
