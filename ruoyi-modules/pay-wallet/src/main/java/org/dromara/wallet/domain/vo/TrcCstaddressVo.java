package org.dromara.wallet.domain.vo;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.wallet.domain.TrcCstaddress;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * TRON钱包地址视图对象 meta_trc20_cstaddressinfo
 *
 * <AUTHOR>
 * @date 2025/1/27
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = TrcCstaddress.class)
public class TrcCstaddressVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * ID标识
     */
    @ExcelProperty(value = "ID标识")
    private Long id;

    /**
     * 客户ID
     */
    @ExcelProperty(value = "客户ID")
    private Long cstId;

    /**
     * 客户钱包地址
     */
    @ExcelProperty(value = "客户钱包地址")
    private String cstAddress;

    /**
     * 客户钱包16进制地址
     */
    @ExcelProperty(value = "客户钱包16进制地址")
    private String cstHexaddress;

    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @ExcelProperty(value = "更新时间")
    private Date updateTime;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;

    /**
     * 二维码
     */
    private String qrcode;

    /**
     * md5校验
     */
    private String md5sum;

    /**
     * 钱包地址
     */
    private String walletAddress;

    /**
     * 合约地址
     */
    private String contractAddress;

    /**
     * 币种类型
     */
    private String coinType;
}
