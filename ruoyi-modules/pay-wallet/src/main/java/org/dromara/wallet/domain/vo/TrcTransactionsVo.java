package org.dromara.wallet.domain.vo;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.wallet.domain.TrcTransactions;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * TRON链交易记录视图对象
 *
 * <AUTHOR>
 * @date 2025/1/27
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = TrcTransactions.class)
public class TrcTransactionsVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ExcelProperty(value = "主键ID")
    private Long id;

    /**
     * 交易ID
     */
    @ExcelProperty(value = "交易ID")
    private String txid;

    /**
     * 区块高度
     */
    @ExcelProperty(value = "区块高度")
    private Long blockHeight;

    /**
     * 接收地址
     */
    @ExcelProperty(value = "接收地址")
    private String address;

    /**
     * 发送地址
     */
    @ExcelProperty(value = "发送地址")
    private String fromaddress;

    /**
     * 合约地址
     */
    @ExcelProperty(value = "合约地址")
    private String contract;

    /**
     * 交易金额
     */
    @ExcelProperty(value = "交易金额")
    private BigDecimal amount;

    /**
     * 手续费
     */
    @ExcelProperty(value = "手续费")
    private BigDecimal fee;

    /**
     * 时间戳
     */
    @ExcelProperty(value = "时间戳")
    private int timestamp;

    /**
     * 交易类型
     */
    @ExcelProperty(value = "交易类型")
    private String type;

    /**
     * 是否同步
     */
    @ExcelProperty(value = "是否同步")
    private int isSync;

    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @ExcelProperty(value = "更新时间")
    private Date updateTime;


}
