package org.dromara.wallet.domain.vo;

import org.dromara.wallet.domain.WithdrawProcessRec;
import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 提款处理记录视图对象 withdraw_process_rec
 *
 * <AUTHOR>
 * @date 2025-07-17
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = WithdrawProcessRec.class)
public class WithdrawProcessRecVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * ID标识
     */
    @ExcelProperty(value = "ID标识")
    private Long id;

    /**
     * 外部关联id（订单id）
     */
    @ExcelProperty(value = "外部关联id", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "订单id")
    private Long orderId;

    /**
     * 区块链交易哈希
     */
    @ExcelProperty(value = "区块链交易哈希")
    private String txid;

    /**
     * 状态（0:转账中，1:成功，2:失败）
     */
    @ExcelProperty(value = "状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "0:转账中，1:成功，2:失败")
    private Integer status;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;


}
