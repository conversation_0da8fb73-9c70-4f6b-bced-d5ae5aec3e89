package org.dromara.wallet.mapper;

import org.apache.ibatis.annotations.Param;
import org.dromara.wallet.domain.MetaBep20Cstaddressinfo;
import org.dromara.wallet.domain.vo.MetaBep20CstaddressinfoVo;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;

import java.util.Date;
import java.util.List;

/**
 * EVM兼容链客户热钱包地址信息Mapper接口
 * 支持所有EVM兼容链：BSC、ARB (Arbitrum)、BASE等
 *
 * <AUTHOR>
 * @date 2025-06-25
 */
public interface MetaBep20CstaddressinfoMapper extends BaseMapperPlus<MetaBep20Cstaddressinfo, MetaBep20CstaddressinfoVo> {

    List<MetaBep20Cstaddressinfo> findAddressesNeedUpdate(@Param("startTime") Date startTime,@Param("chainType") String chainType);

    /**
     * 查询所有钱包地址用于归集
     * @param maxCount 最大返回数量
     * @return 钱包地址列表
     */
    List<MetaBep20Cstaddressinfo> findAllAddressesForCollect(@Param("maxCount") Integer maxCount);

    /**
     * 查询指定地址列表的钱包信息
     * @param addresses 地址列表
     * @return 钱包信息列表
     */
    List<MetaBep20Cstaddressinfo> findAddressesByList(@Param("addresses") List<String> addresses);
}
