package org.dromara.wallet.mapper;

import org.apache.ibatis.annotations.Param;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;
import org.dromara.wallet.domain.MetaSolanaCstaddressinfo;
import org.dromara.wallet.domain.vo.MetaSolanaCstaddressinfoVo;

import java.util.Date;
import java.util.List;

/**
 * Solana客户热钱包信息Mapper接口
 *
 * <AUTHOR>
 * @date 2025-04-15
 */
public interface MetaSolanacstaddressinfoMapper extends BaseMapperPlus<MetaSolanaCstaddressinfo, MetaSolanaCstaddressinfoVo> {

    /**
     * 查询需要更新余额的钱包地址
     * 选择最近24小时内没有记录的地址，并按照地址ID排序，每次返回10个
     *
     * @param startTime 24小时前的时间点
     * @return 需要查询余额的钱包地址列表，最多10个
     */
    List<MetaSolanaCstaddressinfo> findAddressesNeedUpdate(@Param("startTime") Date startTime, @Param("chainType") String chainType);

    /**
     * 查询所有钱包地址用于归集
     * @param maxCount 最大返回数量
     * @return 钱包地址列表
     */
    List<MetaSolanaCstaddressinfo> findAllAddressesForCollect(@Param("maxCount") Integer maxCount);

    /**
     * 查询指定地址列表的钱包信息
     * @param addresses 地址列表
     * @return 钱包信息列表
     */
    List<MetaSolanaCstaddressinfo> findAddressesByList(@Param("addresses") List<String> addresses);
}
