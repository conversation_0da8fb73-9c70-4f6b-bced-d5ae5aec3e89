package org.dromara.wallet.mapper;

import org.apache.ibatis.annotations.Param;
import org.dromara.wallet.domain.MetaTrc20Cstaddressinfo;
import org.dromara.wallet.domain.vo.MetaTrc20CstaddressinfoVo;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;

import java.util.Date;
import java.util.List;

/**
 * TRON客户热钱包地址信息Mapper接口
 *
 * <AUTHOR>
 * @date 2025-06-25
 */
public interface MetaTrc20CstaddressinfoMapper extends BaseMapperPlus<MetaTrc20Cstaddressinfo, MetaTrc20CstaddressinfoVo> {

    List<MetaTrc20Cstaddressinfo> findAddressesNeedUpdate(@Param("startTime") Date startTime, @Param("chainType") String chainType);

    /**
     * 查询所有钱包地址用于归集
     * @param maxCount 最大返回数量
     * @return 钱包地址列表
     */
    List<MetaTrc20Cstaddressinfo> findAllAddressesForCollect(@Param("maxCount") Integer maxCount);

    /**
     * 查询指定地址列表的钱包信息
     * @param addresses 地址列表
     * @return 钱包信息列表
     */
    List<MetaTrc20Cstaddressinfo> findAddressesByList(@Param("addresses") List<String> addresses);

}
