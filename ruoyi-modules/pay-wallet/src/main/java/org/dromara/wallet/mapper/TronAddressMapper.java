package org.dromara.wallet.mapper;

import org.apache.ibatis.annotations.Param;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;
import org.dromara.wallet.domain.TrcCstaddress;
import org.dromara.wallet.domain.vo.TrcCstaddressVo;

import java.util.Date;
import java.util.List;

/**
 * TRON钱包地址Mapper接口
 *
 * <AUTHOR>
 * @date 2025/1/27
 */
public interface TronAddressMapper extends BaseMapperPlus<TrcCstaddress, TrcCstaddressVo> {

    /**
     * 查询需要更新余额的钱包地址
     * 选择最近24小时内没有记录的地址，并按照地址ID排序，每次返回10个
     *
     * @param startTime 24小时前的时间点
     * @return 需要查询余额的钱包地址列表，最多10个
     */
    List<TrcCstaddress> findAddressesNeedUpdate(@Param("startTime") Date startTime, @Param("chainType") String chainType);

}
