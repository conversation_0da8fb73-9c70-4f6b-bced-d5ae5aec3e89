package org.dromara.wallet.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;
import org.dromara.wallet.domain.WalletCoinRec;
import org.dromara.wallet.domain.bo.WalletCoinRecBo;
import org.dromara.wallet.domain.vo.WalletCoinRecVo;

import java.math.BigInteger;
import java.util.List;

/**
 * 多链用户钱包代币余额记录Mapper接口
 *
 * <AUTHOR>
 * @date 2025/1/27
 */
public interface WalletCoinRecMapper extends BaseMapperPlus<WalletCoinRec, WalletCoinRecVo> {

    /**
     * 分页查询每个钱包地址和代币组合的最新记录
     * 使用MyBatis-Plus标准的QueryWrapper方式
     *
     * @param page    分页对象
     * @param wrapper 查询条件包装器
     * @return 最新记录分页列表
     */
    IPage<WalletCoinRecVo> selectLatestRecordsPage(IPage<WalletCoinRecVo> page, @Param("ew") Wrapper<WalletCoinRec> wrapper);
}
