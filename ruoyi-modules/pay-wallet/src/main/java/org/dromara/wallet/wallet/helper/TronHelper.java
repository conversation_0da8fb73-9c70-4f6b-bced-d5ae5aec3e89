package org.dromara.wallet.wallet.helper;

import com.fasterxml.jackson.databind.JsonNode;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.wallet.config.facade.TronConfigFacade;
import org.dromara.wallet.wallet.exception.TronTransferException;
import org.dromara.wallet.wallet.transfer.config.TransactionConfirmationConfig;
import org.dromara.wallet.wallet.transfer.dto.TransactionConfirmationResult;
import org.dromara.wallet.wallet.transfer.service.TransactionConfirmationService;
import org.dromara.wallet.wallet.utils.TronTransactionSigner;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.math.RoundingMode;


/**
 * TRON链操作助手类 - HTTP API版本
 *
 * <p>主要功能包括：</p>
 * <ul>
 *   <li>配置管理和验证</li>
 *   <li>余额查询（TRX和TRC20代币）</li>
 *   <li>转账操作（支持手续费钱包）</li>
 *   <li>资源管理（能量、带宽）</li>
 *   <li>交易确认和验证</li>
 * </ul>
 *
 * <p>架构特点：</p>
 * <ul>
 *   <li>统一的 transfer 方法作为唯一公共转账入口</li>
 *   <li>完全基于 HTTP API，不依赖 TRON SDK</li>
 *   <li>自动区分 TRX 和 TRC20 代币</li>
 *   <li>内置手续费钱包支持和重试机制</li>
 *   <li>对齐 EvmHelper 的设计模式</li>
 * </ul>
 *
 * <AUTHOR>
 * @date 2025-07-09
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TronHelper implements TransactionConfirmationService {

    // ==================== 依赖注入 ====================

    private final TronConfigFacade tronConfigFacade;
    private final TronHttpApiHelper httpApiService;

    // ==================== 余额查询方法 ====================

    /**
     * 获取TRX余额（原始格式）- 内部方法
     * 简化实现，移除复杂重试机制
     *
     * @param address 钱包地址
     * @return TRX余额（Sun单位，1 TRX = 1,000,000 Sun）
     * @throws TronTransferException 当查询失败时抛出异常
     */
    private BigInteger balanceGetNativeRaw(String address) {
        try {
            BigInteger balance = httpApiService.getAccountBalance(address);
            log.debug("TRX余额查询: {} = {} Sun", address, balance);
            return balance;
        } catch (Exception e) {
            log.debug("TRX余额查询失败: {} | {}", address, e.getMessage());
            throw new TronTransferException("获取TRX余额失败: " + e.getMessage());
        }
    }

    /**
     * 获取TRX余额（可读格式）- 基础API方法
     *
     * @param address 钱包地址
     * @return TRX余额（以TRX为单位，保留6位小数）
     */
    public BigDecimal balanceGetNativeForRead(String address) {
        BigInteger balance = balanceGetNativeRaw(address);
        // TRX精度是6位小数（1 TRX = 1,000,000 Sun）
        return new BigDecimal(balance).divide(new BigDecimal("1000000"), 6, RoundingMode.DOWN);
    }

    /**
     * 获取TRC20代币余额（原始格式）- 内部方法
     * 简化实现，移除复杂重试机制
     *
     * @param address         用户地址
     * @param contractAddress 合约地址
     * @return 代币余额（原始单位）
     * @throws TronTransferException 当查询失败时抛出异常
     */
    private BigInteger balanceGetTokenRaw(String address, String contractAddress) {
        try {
            BigInteger balance = httpApiService.getTrc20Balance(address, contractAddress);
            log.debug("TRON: TRC20余额查询 | address: {} | contract: {} | balance: {}",
                address, contractAddress, balance);
            return balance;
        } catch (Exception e) {
            log.error("TRON: TRC20余额查询失败 | address: {} | contract: {} | error: {}",
                address, contractAddress, e.getMessage());
            throw new TronTransferException("获取TRC20余额失败: " + e.getMessage());
        }
    }

    /**
     * 获取TRC20代币余额（可读格式）- 内部方法
     *
     * @param address     钱包地址
     * @param tokenSymbol 代币符号（用于获取合约地址和精度）
     * @return 可读格式的余额（保留6位小数）
     * @throws TronTransferException 当不支持指定代币时抛出异常
     */
    private BigDecimal balanceGetTokenForRead(String address, String tokenSymbol) {
        // 通过代币符号获取合约地址
        String contractAddress = tronConfigFacade.getContractAddress(tokenSymbol);
        if (contractAddress == null || contractAddress.trim().isEmpty()) {
            throw new TronTransferException("TRON链不支持代币: " + tokenSymbol);
        }

        BigInteger rawBalance = balanceGetTokenRaw(address, contractAddress);
        if (rawBalance == null || rawBalance.equals(BigInteger.ZERO)) {
            return BigDecimal.ZERO;
        }

        // 获取代币精度
        int decimals = tronConfigFacade.getTokenDecimals(tokenSymbol);
        BigDecimal divisor = new BigDecimal(10).pow(decimals);

        return new BigDecimal(rawBalance).divide(divisor, 6, RoundingMode.DOWN);
    }

    /**
     * 临时兼容方法：统一余额查询
     *
     * @param address     钱包地址
     * @param tokenSymbol 代币符号
     * @return 余额
     */
    public BigDecimal balanceGetForRead(String address, String tokenSymbol) {
        // 简化实现，仅用于向后兼容
        if ("TRX".equalsIgnoreCase(tokenSymbol)) {
            return balanceGetNativeForRead(address);
        } else {
            return balanceGetTokenForRead(address, tokenSymbol);
        }
    }

    // ==================== 转账操作方法 ====================

    /**
     * TRX转账 - 基础API方法
     *
     * @param fromAddress 发送方地址
     * @param privateKey  发送方私钥
     * @param amount      转账金额（可读格式，以TRX为单位）
     * @param toAddress   接收方地址
     * @return 交易哈希
     * @throws TronTransferException 当转账失败时抛出异常
     */
    public String transferNative(String fromAddress, String privateKey, BigDecimal amount, String toAddress) {

        // 转换金额为Sun（6位小数）
        BigInteger amountSun = amount.multiply(new BigDecimal("1000000")).toBigInteger();

        // 1. 创建TRX转账交易
        JsonNode transaction = httpApiService.createTrxTransaction(fromAddress, toAddress, amountSun.longValue());

        // 2. 签名并广播交易
        String txHash = httpApiService.signAndBroadcastTransaction(transaction, privateKey);

        log.debug("TRX转账执行完成: {} -> {} | {} TRX | txHash: {}",
            fromAddress, toAddress, amount, txHash);
        return txHash;

    }

    /**
     * TRC20代币转账 - 基础API方法（直接使用合约地址）
     *
     * @param fromAddress     发送方地址
     * @param privateKey      发送方私钥
     * @param amount          转账金额（可读格式）
     * @param toAddress       接收方地址
     * @param contractAddress 合约地址
     * @param tokenSymbol     代币符号（用于获取精度）
     * @return 交易哈希
     * @throws TronTransferException 当转账失败时抛出异常
     */
    public String transferTokenByContract(String fromAddress, String privateKey, BigDecimal amount,
                                          String toAddress, String contractAddress, String tokenSymbol) {
        // 获取代币精度并转换金额
        int decimals = tronConfigFacade.getTokenDecimals(tokenSymbol);
        BigInteger amountRaw = amount.multiply(BigDecimal.TEN.pow(decimals)).toBigInteger();

        // 1. 创建TRC20转账交易
        JsonNode transaction = httpApiService.createTrc20Transaction(fromAddress, toAddress,
            contractAddress, amountRaw.longValue());

        // 2. 签名并广播交易
        String txHash = httpApiService.signAndBroadcastTransaction(transaction, privateKey);

        log.info("TRON: TRC20转账成功 | from: {} | to: {} | contract: {} | amount: {} | txHash: {}",
            fromAddress, toAddress, contractAddress, amount, txHash);

        return txHash;
    }

    // 注意：统一转账方法已迁移到TronTransferStrategy
    // 现在使用：UnifiedTransferService 或 TronTransferStrategy.execute()

    // ==================== 地址推导工具方法 ====================

    /**
     * 从私钥推导TRON地址
     *
     * @param privateKey 私钥（十六进制字符串，可选0x前缀）
     * @return TRON地址（Base58格式）
     * @throws IllegalArgumentException 当私钥格式无效时抛出
     */
    public String getAddressFromPrivateKey(String privateKey) {
        try {
            return TronTransactionSigner.getAddressFromPrivateKey(privateKey);
        } catch (Exception e) {
            log.debug("从私钥推导TRON地址失败: {}", e.getMessage());
            throw new IllegalArgumentException("无效的私钥格式: " + e.getMessage(), e);
        }
    }

    // ==================== 交易结果分析方法 ====================

    /**
     * 分析交易执行结果
     * <p>
     * TRON交易成功判断标准：
     * 1. 如果有result字段且为SUCCESS，则成功
     * 2. 如果有receipt字段且result为SUCCESS，则成功
     * 3. 如果有receipt字段但没有result子字段，检查是否有明确的失败标识
     * 4. 如果交易信息存在且没有明确的失败标识，则认为成功（TRON的默认行为）
     *
     * @param txHash 交易哈希
     * @param txInfo 交易详细信息
     * @return 验证结果
     */
    private TransactionVerificationResult analyzeTransactionResult(String txHash, JsonNode txInfo) {
        try {
            log.debug("TRON: 分析交易结果 | txHash: {} | txInfo: {}", txHash, txInfo.toString());

            // 1. 检查顶级result字段（明确的成功标识）
            if (txInfo.has("result")) {
                String result = txInfo.get("result").asText();
                if ("SUCCESS".equalsIgnoreCase(result)) {
                    log.debug("TRON: 交易成功（顶级result字段） | txHash: {}", txHash);
                    return TransactionVerificationResult.success();
                } else {
                    // 明确的失败
                    String errorMessage = extractErrorMessage(txInfo);
                    log.debug("TRON: 交易失败（顶级result字段） | txHash: {} | result: {}", txHash, result);
                    return TransactionVerificationResult.failure(errorMessage);
                }
            }

            // 2. 检查receipt字段（智能合约执行结果）
            if (txInfo.has("receipt")) {
                JsonNode receipt = txInfo.get("receipt");

                // 检查receipt中的result字段
                if (receipt.has("result")) {
                    String receiptResult = receipt.get("result").asText();
                    if ("SUCCESS".equalsIgnoreCase(receiptResult)) {
                        log.debug("TRON: 交易成功（receipt.result字段） | txHash: {}", txHash);
                        return TransactionVerificationResult.success();
                    } else {
                        // 智能合约执行失败
                        String errorMessage = extractErrorMessage(txInfo);
                        log.debug("TRON: 交易失败（receipt.result字段） | txHash: {} | result: {}", txHash, receiptResult);
                        return TransactionVerificationResult.failure(errorMessage);
                    }
                }

                // 有receipt但没有result字段，检查是否有明确的失败标识
                // 对于TRX转账，通常没有receipt.result字段，这是正常的
                log.debug("TRON: receipt存在但无result字段 | txHash: {}", txHash);
            }

            // 3. 检查是否有明确的错误标识
            if (txInfo.has("resMessage")) {
                String resMessage = txInfo.get("resMessage").asText();
                if (resMessage != null && !resMessage.trim().isEmpty()) {
                    // 有错误消息，说明执行失败
                    String errorMessage = extractErrorMessage(txInfo);
                    log.debug("TRON: 交易失败（有resMessage） | txHash: {} | resMessage: {}", txHash, resMessage);
                    return TransactionVerificationResult.failure(errorMessage);
                }
            }

            // 4. 对于智能合约交易，必须有receipt且result为SUCCESS才算成功
            // 检查交易是否存在基本信息（说明交易已上链）
            if (txInfo.has("id") || txInfo.has("blockNumber") || txInfo.has("blockTimeStamp")) {
                // 如果是智能合约交易但没有receipt或receipt.result不是SUCCESS，则失败
                if (txInfo.has("receipt")) {
                    JsonNode receipt = txInfo.get("receipt");
                    if (!receipt.has("result")) {
                        // 智能合约交易必须有receipt.result字段
                        log.debug("TRON: 智能合约交易失败（缺少receipt.result） | txHash: {}", txHash);
                        return TransactionVerificationResult.failure("智能合约执行状态未知");
                    }
                    // 如果有receipt.result但不是SUCCESS，在步骤2中已经处理
                }

                // 对于普通TRX转账（没有receipt），交易上链即成功
                log.debug("TRON: 交易成功（已上链且无失败标识） | txHash: {}", txHash);
                return TransactionVerificationResult.success();
            }

            // 5. 如果交易信息为空或不完整，认为状态不明确
            log.debug("TRON: 交易状态不明确 | txHash: {} | 交易信息不完整", txHash);
            return TransactionVerificationResult.unclear("交易信息不完整，无法确定状态");

        } catch (Exception e) {
            log.warn("TRON: 分析交易结果异常 | txHash: {} | error: {}", txHash, e.getMessage());
            return TransactionVerificationResult.unclear("分析交易结果时发生异常: " + e.getMessage());
        }
    }

    /**
     * 从交易信息中提取错误消息
     */
    private String extractErrorMessage(JsonNode txInfo) {
        StringBuilder errorMessage = new StringBuilder();

        // 检查resMessage字段（包含详细错误信息）
        if (txInfo.has("resMessage")) {
            String resMessage = txInfo.get("resMessage").asText();
            if (resMessage != null && !resMessage.trim().isEmpty()) {
                // resMessage通常是十六进制编码的，需要解码
                try {
                    String decodedMessage = hexToString(resMessage);
                    if (!decodedMessage.trim().isEmpty()) {
                        errorMessage.append("执行错误: ").append(decodedMessage);
                    }
                } catch (Exception e) {
                    // 如果解码失败，使用原始消息
                    errorMessage.append("执行错误(原始): ").append(resMessage);
                }
            }
        }

        // 检查result字段
        if (txInfo.has("result")) {
            String result = txInfo.get("result").asText();
            if (!"SUCCESS".equalsIgnoreCase(result)) {
                if (!errorMessage.isEmpty()) {
                    errorMessage.append(", ");
                }
                errorMessage.append("交易结果: ").append(result);
            }
        }

        // 检查receipt中的错误信息
        if (txInfo.has("receipt")) {
            JsonNode receipt = txInfo.get("receipt");
            if (receipt.has("result")) {
                String receiptResult = receipt.get("result").asText();
                if (!"SUCCESS".equalsIgnoreCase(receiptResult)) {
                    if (!errorMessage.isEmpty()) {
                        errorMessage.append(", ");
                    }
                    errorMessage.append("合约执行结果: ").append(receiptResult);
                }
            }
        }

        return !errorMessage.isEmpty() ? errorMessage.toString() : "未知错误";
    }

    /**
     * 将十六进制字符串转换为普通字符串
     */
    private String hexToString(String hex) {
        if (hex == null || hex.trim().isEmpty()) {
            return "";
        }

        // 移除0x前缀
        if (hex.startsWith("0x")) {
            hex = hex.substring(2);
        }

        try {
            StringBuilder result = new StringBuilder();
            for (int i = 0; i < hex.length(); i += 2) {
                String hexByte = hex.substring(i, Math.min(i + 2, hex.length()));
                int decimal = Integer.parseInt(hexByte, 16);
                if (decimal != 0) { // 跳过null字符
                    result.append((char) decimal);
                }
            }
            return result.toString();
        } catch (Exception e) {
            log.debug("十六进制解码失败: {}, error: {}", hex, e.getMessage());
            return hex; // 解码失败时返回原始字符串
        }
    }

    /**
     * 交易验证结果内部类
     */
    @Getter
    private static class TransactionVerificationResult {
        private final boolean success;
        private final boolean definitiveFailure;
        private final String errorMessage;

        private TransactionVerificationResult(boolean success, boolean definitiveFailure, String errorMessage) {
            this.success = success;
            this.definitiveFailure = definitiveFailure;
            this.errorMessage = errorMessage;
        }

        public static TransactionVerificationResult success() {
            return new TransactionVerificationResult(true, false, null);
        }

        public static TransactionVerificationResult failure(String errorMessage) {
            return new TransactionVerificationResult(false, true, errorMessage);
        }

        public static TransactionVerificationResult unclear(String message) {
            return new TransactionVerificationResult(false, false, message);
        }

    }

    // ==================== 统一确认接口实现 ====================

    @Override
    public TransactionConfirmationResult confirmTransaction(String txHash, TransactionConfirmationConfig config) {
        try {
            log.debug("TRON统一确认开始: txHash={}, config={}", txHash, config.getDescription());

            // 实现真正的轮询确认逻辑
            long startTime = System.currentTimeMillis();
            long maxWaitTime = config.getTotalMaxWaitTimeMs();
            long checkInterval = config.getCheckIntervalMs();

            while (System.currentTimeMillis() - startTime < maxWaitTime) {
                try {
                    // 查询交易详细状态
                    JsonNode txInfo = httpApiService.getTransactionInfoById(txHash);

                    if (txInfo != null) {
                        // 分析交易执行结果
                        TransactionVerificationResult verificationResult = analyzeTransactionResult(txHash, txInfo);

                        if (verificationResult.isSuccess()) {
                            log.debug("TRON统一确认成功: txHash={}", txHash);
                            return TransactionConfirmationResult.success(txHash, 1); // TRON通常1个确认即可
                        } else if (verificationResult.isDefinitiveFailure()) {
                            // 交易明确失败，不是超时
                            log.warn("TRON: 交易执行失败 | txHash: {} | error: {}",
                                txHash, verificationResult.getErrorMessage());
                            return TransactionConfirmationResult.failure(txHash,
                                org.dromara.wallet.wallet.transfer.enums.TransactionStatus.FAILED,
                                verificationResult.getErrorMessage());
                        }
                        // 如果状态不明确，继续轮询
                    }

                    // 等待下次检查
                    Thread.sleep(checkInterval);

                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    log.warn("TRON确认过程被中断: txHash={}", txHash);
                    return TransactionConfirmationResult.failure(txHash,
                        org.dromara.wallet.wallet.transfer.enums.TransactionStatus.UNKNOWN,
                        "确认过程被中断");
                } catch (Exception e) {
                    log.debug("TRON确认轮询异常，继续重试: txHash={}, error={}", txHash, e.getMessage());
                    // 继续轮询，不立即失败
                }
            }

            // 真正的超时
            log.warn("TRON统一确认超时: txHash={}, timeout={}ms", txHash, maxWaitTime);
            return TransactionConfirmationResult.timeout(txHash, "TRON交易确认超时");

        } catch (Exception e) {
            log.error("TRON统一确认异常: txHash={}, error={}", txHash, e.getMessage());
            return TransactionConfirmationResult.failure(txHash,
                org.dromara.wallet.wallet.transfer.enums.TransactionStatus.UNKNOWN,
                "确认过程异常: " + e.getMessage());
        }
    }

    // ==================== 交易验证方法 ====================

    /**
     * 简化的确认方法 - 不抛出异常版本
     *
     * @param txHash 交易哈希
     * @param waitMs 等待时间（毫秒）
     * @return 是否确认成功
     */
    private boolean performSimpleConfirmation(String txHash, long waitMs) {
        try {
            // 等待指定时间（基于TRON网络3秒区块时间）
            Thread.sleep(waitMs);

            // 查询交易详细状态
            JsonNode txInfo = httpApiService.getTransactionInfoById(txHash);
            if (txInfo != null) {
                // 分析交易执行结果
                TransactionVerificationResult verificationResult = analyzeTransactionResult(txHash, txInfo);

                if (verificationResult.isSuccess()) {
                    log.debug("TRON: 交易确认成功 | txHash: {}", txHash);
                    return true;
                } else if (verificationResult.isDefinitiveFailure()) {
                    // 交易明确失败，记录错误但不抛出异常
                    log.warn("TRON: 交易执行失败 | txHash: {} | error: {}",
                        txHash, verificationResult.getErrorMessage());
                    return false;
                } else {
                    // 交易状态不明确，视为未确认
                    log.debug("TRON: 交易状态不明确 | txHash: {} | message: {}",
                        txHash, verificationResult.getErrorMessage());
                    return false;
                }
            } else {
                // 没有交易信息，可能还未上链
                log.debug("TRON: 交易信息未找到 | txHash: {}", txHash);
                return false;
            }

        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.warn("TRON: 确认过程被中断 | txHash: {}", txHash);
            return false;
        } catch (Exception e) {
            log.warn("TRON: 确认过程异常 | txHash: {} | error: {}", txHash, e.getMessage());
            return false;
        }
    }

    @Override
    public TransactionConfirmationConfig getDefaultConfirmationConfig() {
        return TransactionConfirmationConfig.tronDefault();
    }

    @Override
    public String getBlockchainType() {
        return "TRON";
    }

}
