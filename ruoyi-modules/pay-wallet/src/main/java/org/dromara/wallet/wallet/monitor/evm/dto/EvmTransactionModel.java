package org.dromara.wallet.wallet.monitor.evm.dto;

import lombok.Builder;
import lombok.Data;
import org.web3j.protocol.core.methods.response.Log;

import java.math.BigDecimal;
import java.math.BigInteger;

/**
 * EVM交易数据模型
 * 用于封装EVM链（ETH、BSC、ARB、BASE等）的交易信息
 *
 * <p>功能特性：</p>
 * <ul>
 *   <li>统一的EVM交易数据结构</li>
 *   <li>支持Transfer事件解析</li>
 *   <li>包含完整的交易元数据</li>
 *   <li>支持交易状态跟踪</li>
 * </ul>
 *
 * <AUTHOR>
 * @date 2025/7/11
 */
@Data
@Builder
public class EvmTransactionModel {

    // ============ 基础交易信息 ============

    /**
     * 交易哈希
     */
    private String transactionHash;

    /**
     * 区块号
     */
    private BigInteger blockNumber;

    /**
     * 区块哈希
     */
    private String blockHash;

    /**
     * 交易在区块中的索引
     */
    private BigInteger transactionIndex;

    /**
     * 交易时间戳（秒）
     */
    private Long timestamp;

    // ============ Transfer事件信息 ============

    /**
     * 合约地址
     */
    private String contractAddress;

    /**
     * 发送方地址
     */
    private String fromAddress;

    /**
     * 接收方地址
     */
    private String toAddress;

    /**
     * 转账金额（原始单位，未转换精度）
     */
    private BigInteger rawAmount;

    /**
     * 转账金额（已转换精度）
     */
    private BigDecimal amount;

    /**
     * 代币符号
     */
    private String tokenSymbol;

    /**
     * 代币精度
     */
    private Integer tokenDecimals;

    // ============ Gas信息 ============

    /**
     * Gas价格
     */
    private BigInteger gasPrice;

    /**
     * Gas使用量
     */
    private BigInteger gasUsed;

    /**
     * 交易手续费（ETH/BNB等原生代币）
     */
    private BigDecimal transactionFee;

    // ============ 业务处理信息 ============

    /**
     * 交易类型
     * receive: 接收交易
     * send: 发送交易
     * collect: 归集交易
     */
    private String transactionType;

    /**
     * 处理状态
     * 0: 待处理
     * 1: 处理完成
     * 2: 不需要处理
     * 4: 不符合最小入账
     * 5: 该地址余额不正确
     */
    private Integer processStatus;

    /**
     * 是否可信
     * 用于标识交易的可信度
     */
    private Boolean trustworthy;

    /**
     * 链名称
     */
    private String chainName;

    /**
     * 链ID
     */
    private Long chainId;

    // ============ 原始数据 ============

    /**
     * 原始Log对象
     * 保留原始数据以备后续处理
     */
    private Log originalLog;

    // ============ 扩展信息 ============

    /**
     * 错误信息
     * 处理失败时的错误描述
     */
    private String errorMessage;

    /**
     * 创建时间
     */
    private Long createTime;

    /**
     * 更新时间
     */
    private Long updateTime;

    /**
     * 重试次数
     * 记录当前交易已经重试的次数
     */
    private Integer retryCount;

    // ============ 便捷方法 ============

    /**
     * 获取格式化的交易信息
     */
    public String getFormattedInfo() {
        return String.format("[%s] %s -> %s: %s %s (Hash: %s)",
            chainName,
            fromAddress,
            toAddress,
            amount,
            tokenSymbol,
            transactionHash);
    }

    /**
     * 从Log对象创建交易模型
     */
    public static EvmTransactionModel fromLog(Log log) {
        return EvmTransactionModel.builder()
            .transactionHash(log.getTransactionHash())
            .blockNumber(log.getBlockNumber())
            .blockHash(log.getBlockHash())
            .transactionIndex(log.getTransactionIndex())
            .contractAddress(log.getAddress())
            .originalLog(log)
            .createTime(System.currentTimeMillis())
            .trustworthy(true)
            .processStatus(0)
            .retryCount(0)
            .build();
    }
}
