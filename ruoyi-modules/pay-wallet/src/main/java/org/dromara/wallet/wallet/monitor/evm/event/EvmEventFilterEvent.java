package org.dromara.wallet.wallet.monitor.evm.event;

import lombok.extern.slf4j.Slf4j;
import org.dromara.common.scanning.chain.model.TransactionModel;
import org.dromara.common.scanning.monitor.EthMonitorEvent;
import org.dromara.common.scanning.monitor.filter.EthMonitorFilter;
import org.dromara.wallet.config.facade.EvmConfigFacade;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.web3j.protocol.core.methods.response.Log;

import java.math.BigInteger;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * EVM事件过滤事件
 * 负责过滤Transfer事件并按监控合约地址筛选
 *
 * <p>功能特性：</p>
 * <ul>
 *   <li>Transfer事件过滤：只保留Transfer类型的事件日志</li>
 *   <li>合约地址过滤：只保留监控合约地址的事件</li>
 *   <li>数据清洗：移除不相关的事件日志</li>
 *   <li>性能优化：减少后续处理的数据量</li>
 * </ul>
 *
 * <AUTHOR>
 * @date 2025/7/24
 */
@Slf4j
@Component
public class EvmEventFilterEvent implements EthMonitorEvent {

    /**
     * 链配置门面映射表
     * key: 链名称 (BSC, ARB, BASE)
     * value: 对应的EvmConfigFacade实现
     */
    private final Map<String, EvmConfigFacade> configFacadeMap;

    /**
     * 构造函数，自动注入所有EvmConfigFacade实现并构建映射表
     */
    @Autowired
    public EvmEventFilterEvent(List<EvmConfigFacade> configFacades) {
        this.configFacadeMap = configFacades.stream()
            .collect(Collectors.toMap(
                EvmConfigFacade::getChainName,
                Function.identity()
            ));

        log.info("EvmEventFilterEvent初始化完成，支持的链: {}", configFacadeMap.keySet());
    }

    /**
     * 过滤后的Log存储键名
     */
    public static final String FILTERED_LOG_KEY = "evm.filteredLog";

    /**
     * Transfer事件的topic签名
     * keccak256("Transfer(address,address,uint256)")
     */
    private static final String TRANSFER_EVENT_TOPIC = "0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef";

    @Override
    public EthMonitorFilter ethMonitorFilter() {
        // 不在这里进行过滤，接受所有交易进行事件过滤
        return EthMonitorFilter.builder()
            .setMinValue(BigInteger.ZERO);
    }

    @Override
    public void call(TransactionModel transactionModel) {
        try {
            // 获取链类型标识
            String chainType = transactionModel.getChainType();
            if (chainType == null) {
                log.debug("交易模型缺少链类型标识，跳过事件过滤");
                storeFilteredLog(transactionModel, null);
                return;
            }

            // 获取对应链的配置门面
            EvmConfigFacade configFacade = getConfigFacade(chainType);
            if (configFacade == null) {
                log.debug("{}链没有对应的配置门面，跳过事件过滤", chainType);
                storeFilteredLog(transactionModel, null);
                return;
            }

            // 检查是否为EVM交易模型
            if (transactionModel.getEthTransactionModel() == null) {
                log.debug("{}链跳过非EVM交易模型", chainType);
                storeFilteredLog(transactionModel, null);
                return;
            }

            // 获取Log对象
            Log logData = transactionModel.getEthTransactionModel().getLog();
            if (logData == null) {
                log.debug("{}链交易没有Log数据，跳过事件过滤", chainType);
                storeFilteredLog(transactionModel, null);
                return;
            }

            String txHash = logData.getTransactionHash();
            if (txHash == null) {
                log.debug("{}链交易哈希为空，跳过事件过滤", chainType);
                storeFilteredLog(transactionModel, null);
                return;
            }

            // 执行事件过滤
            Log filteredLog = filterEvent(logData, txHash, configFacade);

            // 存储过滤后的Log供后续Event使用
            storeFilteredLog(transactionModel, filteredLog);

            if (filteredLog != null) {
                log.debug("{}链交易{}通过事件过滤检查", chainType, txHash);
            } else {
                log.debug("{}链交易{}未通过事件过滤检查", chainType, txHash);
            }

        } catch (Exception e) {
            String chainType = transactionModel.getChainType();
            log.error("{}链事件过滤失败: {}", chainType != null ? chainType : "UNKNOWN", e.getMessage());
            // 异常时存储null，避免后续Event出错
            storeFilteredLog(transactionModel, null);
        }
    }

    /**
     * 根据链类型获取对应的配置门面
     */
    private EvmConfigFacade getConfigFacade(String chainType) {
        return configFacadeMap.get(chainType);
    }

    /**
     * 过滤事件日志
     */
    private Log filterEvent(Log logData, String txHash, EvmConfigFacade configFacade) {
        // 1. 检查是否为Transfer事件
        if (!isTransferEvent(logData)) {
            log.debug("{}链交易{}不是Transfer事件", configFacade.getChainName(), txHash);
            return null;
        }

        // 2. 检查合约地址是否在监控列表中
        String contractAddress = logData.getAddress();
        if (!isMonitoredContract(contractAddress, configFacade)) {
            log.debug("{}链交易{}的合约地址{}不在监控列表中",
                configFacade.getChainName(), txHash, contractAddress);
            return null;
        }

        // 3. 验证Transfer事件的格式
        if (!isValidTransferEvent(logData)) {
            log.debug("{}链交易{}的Transfer事件格式无效", configFacade.getChainName(), txHash);
            return null;
        }

        log.debug("{}链交易{}通过所有事件过滤条件", configFacade.getChainName(), txHash);
        return logData;
    }

    /**
     * 检查是否为Transfer事件
     */
    private boolean isTransferEvent(Log logData) {
        List<String> topics = logData.getTopics();
        if (topics == null || topics.isEmpty()) {
            return false;
        }

        return TRANSFER_EVENT_TOPIC.equals(topics.get(0));
    }

    /**
     * 检查合约地址是否在监控列表中
     */
    private boolean isMonitoredContract(String contractAddress, EvmConfigFacade configFacade) {
        if (contractAddress == null) {
            return false;
        }

        Set<String> monitoredContractAddresses = getMonitoredContractAddresses(configFacade);
        if (monitoredContractAddresses.isEmpty()) {
            // 如果没有配置监控合约地址，则接受所有合约
            log.debug("{}链没有配置监控合约地址，接受所有合约", configFacade.getChainName());
            return true;
        }

        return monitoredContractAddresses.contains(contractAddress.toLowerCase());
    }

    /**
     * 验证Transfer事件的格式
     */
    private boolean isValidTransferEvent(Log logData) {
        List<String> topics = logData.getTopics();

        // Transfer事件应该有3个topics: event signature, from, to
        if (topics.size() != 3) {
            return false;
        }

        // 检查data字段是否存在（包含amount信息）
        String data = logData.getData();
        if (data == null || data.length() < 66) { // 0x + 64个字符（32字节）
            return false;
        }

        return true;
    }

    /**
     * 获取监控的合约地址集合
     */
    private Set<String> getMonitoredContractAddresses(EvmConfigFacade configFacade) {
        return configFacade.getEnabledContractAddresses().stream()
            .map(String::toLowerCase)
            .collect(Collectors.toSet());
    }

    /**
     * 存储过滤后的Log
     */
    private void storeFilteredLog(TransactionModel transactionModel, Log filteredLog) {
        // 简化：不再存储，直接在EvmBusinessProcessEvent中处理
        if (transactionModel.getEthTransactionModel() != null) {
            String txHash = filteredLog != null ? filteredLog.getTransactionHash() : "unknown";
            String chainType = transactionModel.getChainType();
            log.trace("{}链交易{}过滤后的Log: {}",
                chainType != null ? chainType : "UNKNOWN", txHash, filteredLog != null ? "有效" : "无效");
        }
    }

    /**
     * 静态方法：获取过滤后的Log
     * 供后续的MonitorEvent使用
     */
    public static Log getFilteredLog(TransactionModel transactionModel) {
        // 简化：直接返回原始Log，让EvmBusinessProcessEvent处理
        if (transactionModel.getEthTransactionModel() != null) {
            return transactionModel.getEthTransactionModel().getLog();
        }
        return null;
    }

    /**
     * 静态方法：检查是否有有效的过滤后Log
     * 供后续的MonitorEvent使用
     */
    public static boolean hasValidFilteredLog(TransactionModel transactionModel) {
        Log filteredLog = getFilteredLog(transactionModel);
        return filteredLog != null;
    }
}
