package org.dromara.wallet.wallet.monitor.evm.event;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.redis.utils.RedisUtils;
import org.dromara.common.scanning.chain.model.TransactionModel;
import org.dromara.common.scanning.monitor.EthMonitorEvent;
import org.dromara.common.scanning.monitor.filter.EthMonitorFilter;
import org.dromara.wallet.config.facade.EvmConfigFacade;
import org.redisson.api.RBucket;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigInteger;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * EVM扫描进度保存事件
 * 专门负责保存扫描进度到Redis，从业务处理事件中分离出来
 *
 * <p>功能特性：</p>
 * <ul>
 *   <li>扫描进度持久化：将当前扫描的区块号保存到Redis</li>
 *   <li>多链支持：支持BSC、ARB、BASE等EVM链的独立进度管理</li>
 *   <li>防重复保存：相同区块号不重复保存，提高性能</li>
 *   <li>错误处理：保存失败时记录错误但不影响业务处理</li>
 *   <li>可选择性：手动扫描时可以不使用此事件，避免进度覆盖</li>
 * </ul>
 *
 * <p>设计目的：</p>
 * <ul>
 *   <li>解决手动扫描可能覆盖正常扫描进度的问题</li>
 *   <li>将进度保存逻辑从业务处理中分离，提高代码清晰度</li>
 *   <li>为不同类型的扫描（正常扫描vs手动扫描）提供灵活性</li>
 * </ul>
 *
 * <p>使用场景：</p>
 * <ul>
 *   <li>正常的区块链扫描：需要保存进度</li>
 *   <li>手动区块扫描：不需要保存进度，避免覆盖</li>
 *   <li>补偿扫描：根据需要决定是否保存进度</li>
 * </ul>
 *
 * <AUTHOR>
 * @date 2025/7/25
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class EvmScanProgressEvent implements EthMonitorEvent {

    /**
     * 链配置门面映射表
     * key: 链名称 (BSC, ARB, BASE)
     * value: 对应的EvmConfigFacade实现
     */
    private final Map<String, EvmConfigFacade> configFacadeMap;

    /**
     * 每个链的最后保存的区块号缓存
     * 用于避免重复保存相同的区块号
     */
    private final Map<String, BigInteger> lastSavedBlockMap = new ConcurrentHashMap<>();

    /**
     * 构造函数，自动注入所有EvmConfigFacade实现并构建映射表
     */
    @Autowired
    public EvmScanProgressEvent(List<EvmConfigFacade> configFacades) {
        this.configFacadeMap = configFacades.stream()
            .collect(Collectors.toMap(
                EvmConfigFacade::getChainName,
                Function.identity()
            ));

        log.info("EvmScanProgressEvent初始化完成，支持的链: {}", configFacadeMap.keySet());
    }

    @Override
    public EthMonitorFilter ethMonitorFilter() {
        // 不进行任何过滤，接受所有交易用于进度更新
        return EthMonitorFilter.builder()
            .setMinValue(BigInteger.ZERO);
    }

    @Override
    public void call(TransactionModel transactionModel) {
        try {
            // 1. 检查是否为EVM交易模型
            if (transactionModel.getEthTransactionModel() == null) {
                return;
            }

            // 2. 获取链类型和配置门面
            String chainType = transactionModel.getChainType();
            if (chainType == null) {
                log.debug("交易模型缺少链类型信息，跳过进度保存");
                return;
            }

            EvmConfigFacade configFacade = configFacadeMap.get(chainType.toUpperCase());
            if (configFacade == null) {
                log.debug("未找到{}链的配置门面，跳过进度保存", chainType);
                return;
            }

            // 3. 获取区块号
            BigInteger blockNumber = getBlockNumber(transactionModel);
            if (blockNumber == null) {
                log.debug("{}链无法获取区块号，跳过进度保存", chainType);
                return;
            }

            // 4. 保存扫描进度
            saveProgressIfNeeded(configFacade.getChainName(), blockNumber);

        } catch (Exception e) {
            // 进度保存失败不应该影响业务处理，只记录错误
            log.error("{}链扫描进度保存失败: blockNumber={}, error={}",
                getChainType(transactionModel),
                getBlockNumber(transactionModel),
                e.getMessage());
        }
    }

    /**
     * 保存扫描进度（如果需要的话）
     * 避免重复保存相同的区块号
     */
    private void saveProgressIfNeeded(String chainName, BigInteger blockNumber) {
        try {
            // 检查是否需要保存（避免重复保存相同区块号）
            BigInteger lastSaved = lastSavedBlockMap.get(chainName);
            if (lastSaved != null && lastSaved.compareTo(blockNumber) >= 0) {
                // 当前区块号不大于已保存的区块号，跳过保存
                return;
            }

            // 保存到Redis
            String redisKey = chainName.toLowerCase() + "_current_block";
            RBucket<BigInteger> bucket = RedisUtils.getClient().getBucket(redisKey);
            bucket.set(blockNumber);

            // 更新缓存
            lastSavedBlockMap.put(chainName, blockNumber);

            log.debug("{}链扫描进度已保存: {}", chainName, blockNumber);

        } catch (Exception e) {
            log.error("{}链扫描进度保存到Redis失败: blockNumber={}, error={}",
                chainName, blockNumber, e.getMessage());
        }
    }

    /**
     * 从交易模型中获取区块号
     */
    private BigInteger getBlockNumber(TransactionModel transactionModel) {
        try {
            if (transactionModel.getEthTransactionModel() != null &&
                transactionModel.getEthTransactionModel().getLog() != null) {
                return transactionModel.getEthTransactionModel().getLog().getBlockNumber();
            }
        } catch (Exception e) {
            log.debug("获取区块号失败: {}", e.getMessage());
        }
        return null;
    }

    /**
     * 获取链类型
     */
    private String getChainType(TransactionModel transactionModel) {
        return transactionModel.getChainType() != null ? transactionModel.getChainType() : "UNKNOWN";
    }

    /**
     * 手动清除指定链的进度缓存
     * 用于重置或强制更新进度
     */
    public void clearProgressCache(String chainName) {
        lastSavedBlockMap.remove(chainName.toUpperCase());
        log.info("{}链扫描进度缓存已清除", chainName);
    }

    /**
     * 获取指定链的最后保存区块号
     */
    public BigInteger getLastSavedBlock(String chainName) {
        return lastSavedBlockMap.get(chainName.toUpperCase());
    }

    /**
     * 手动设置指定链的进度
     * 用于初始化或强制设置进度
     */
    public void setProgress(String chainName, BigInteger blockNumber) {
        try {
            String redisKey = chainName.toLowerCase() + "_current_block";
            RBucket<BigInteger> bucket = RedisUtils.getClient().getBucket(redisKey);
            bucket.set(blockNumber);

            // 更新缓存
            lastSavedBlockMap.put(chainName.toUpperCase(), blockNumber);

            log.info("{}链扫描进度已手动设置: {}", chainName, blockNumber);

        } catch (Exception e) {
            log.error("{}链手动设置扫描进度失败: blockNumber={}, error={}",
                chainName, blockNumber, e.getMessage());
        }
    }
}
