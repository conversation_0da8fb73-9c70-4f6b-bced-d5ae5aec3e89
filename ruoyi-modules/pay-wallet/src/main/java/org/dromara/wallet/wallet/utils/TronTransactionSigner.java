package org.dromara.wallet.wallet.utils;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.bouncycastle.asn1.x9.X9ECParameters;
import org.bouncycastle.crypto.digests.SHA256Digest;
import org.bouncycastle.crypto.ec.CustomNamedCurves;
import org.bouncycastle.crypto.params.ECDomainParameters;
import org.bouncycastle.crypto.params.ECPrivateKeyParameters;
import org.bouncycastle.crypto.signers.ECDSASigner;
import org.bouncycastle.crypto.signers.HMacDSAKCalculator;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.bouncycastle.math.ec.ECPoint;
import org.bouncycastle.util.encoders.Hex;

import java.math.BigInteger;
import java.security.MessageDigest;
import java.security.Security;
import java.util.Arrays;
import java.util.List;

/**
 * TRON交易签名工具类
 * 基于椭圆曲线数字签名算法(ECDSA)实现TRON交易签名
 *
 * <AUTHOR>
 * @date 2025-06-30
 */
@Slf4j
public class TronTransactionSigner {

    private static final ObjectMapper objectMapper = new ObjectMapper();

    // secp256k1椭圆曲线参数
    private static final X9ECParameters CURVE_PARAMS = CustomNamedCurves.getByName("secp256k1");
    private static final ECDomainParameters DOMAIN_PARAMS = new ECDomainParameters(
        CURVE_PARAMS.getCurve(), CURVE_PARAMS.getG(), CURVE_PARAMS.getN(), CURVE_PARAMS.getH());

    static {
        // 添加BouncyCastle提供者
        if (Security.getProvider(BouncyCastleProvider.PROVIDER_NAME) == null) {
            Security.addProvider(new BouncyCastleProvider());
        }
    }

    /**
     * 签名TRON交易
     *
     * @param transactionResponse 未签名的交易响应JSON对象（可能包含transaction字段）
     * @param privateKeyHex 私钥十六进制字符串
     * @return 已签名的交易JSON对象
     */
    public static JsonNode signTransaction(JsonNode transactionResponse, String privateKeyHex) {
        try {
            log.debug("开始签名TRON交易: {}", transactionResponse.toString());

            if (privateKeyHex == null || privateKeyHex.trim().isEmpty()) {
                throw new IllegalArgumentException("私钥不能为空");
            }

            // 移除0x前缀
            if (privateKeyHex.startsWith("0x")) {
                privateKeyHex = privateKeyHex.substring(2);
            }

            // 验证私钥长度
            if (privateKeyHex.length() != 64) {
                throw new IllegalArgumentException("私钥长度必须是64位十六进制字符");
            }

            // 提取实际的transaction对象
            JsonNode transaction = transactionResponse;
            if (transactionResponse.has("transaction")) {
                transaction = transactionResponse.get("transaction");
            }

            // 获取交易哈希并签名
            String txHash = getTransactionHash(transactionResponse);
            String signature = signHash(txHash, privateKeyHex);

            // 构造已签名的交易
            ObjectNode signedTransaction = objectMapper.createObjectNode();
            signedTransaction.setAll((ObjectNode) transaction);
            signedTransaction.set("signature", objectMapper.valueToTree(List.of(signature)));

            // 使用专用签名日志器记录成功信息
            log.debug("交易签名成功: {}", txHash);
            return signedTransaction;

        } catch (Exception e) {
            log.debug("交易签名失败: {}", e.getMessage());
            throw new RuntimeException("交易签名失败: " + e.getMessage(), e);
        }
    }

    /**
     * 计算交易哈希
     */
    private static String getTransactionHash(JsonNode transactionResponse) {
        try {
            log.debug("开始计算交易哈希，输入数据: {}", transactionResponse.toString());

            // 首先检查是否有transaction字段（完整响应格式）
            JsonNode transaction = transactionResponse;
            if (transactionResponse.has("transaction")) {
                transaction = transactionResponse.get("transaction");
                log.debug("从响应中提取transaction字段");
            }

            String rawDataHex = null;

            // 优先使用raw_data_hex字段
            if (transaction.has("raw_data_hex")) {
                JsonNode rawDataHexNode = transaction.get("raw_data_hex");
                if (rawDataHexNode != null && !rawDataHexNode.isNull()) {
                    rawDataHex = rawDataHexNode.asText();
                    log.debug("从raw_data_hex字段获取数据: {}", rawDataHex);
                }
            }
            // 如果没有raw_data_hex，尝试使用raw_data字段
            else if (transaction.has("raw_data")) {
                JsonNode rawDataNode = transaction.get("raw_data");
                if (rawDataNode != null && !rawDataNode.isNull()) {
                    // 如果raw_data是字符串格式的hex
                    if (rawDataNode.isTextual()) {
                        rawDataHex = rawDataNode.asText();
                        log.debug("从raw_data字段获取hex数据: {}", rawDataHex);
                    } else {
                        // 如果raw_data是对象格式，暂时不支持
                        throw new IllegalArgumentException("不支持的raw_data对象格式，需要raw_data_hex字段");
                    }
                }
            }

            if (rawDataHex == null || rawDataHex.trim().isEmpty()) {
                throw new IllegalArgumentException("无法从交易中提取raw_data_hex数据，请检查交易格式");
            }

            // 移除0x前缀
            if (rawDataHex.startsWith("0x")) {
                rawDataHex = rawDataHex.substring(2);
            }

            // 转换为字节数组
            byte[] rawData = Hex.decode(rawDataHex);

            // 计算SHA256哈希
            byte[] hash = sha256(rawData);

            String hashHex = Hex.toHexString(hash);
            log.debug("计算得到的交易哈希: {}", hashHex);

            return hashHex;

        } catch (Exception e) {
            log.error("计算交易哈希失败: {}", e.getMessage());
            throw new RuntimeException("计算交易哈希失败: " + e.getMessage(), e);
        }
    }

    /**
     * 使用私钥签名哈希
     */
    private static String signHash(String hashHex, String privateKeyHex) {
        try {
            // 转换为字节数组
            byte[] hash = Hex.decode(hashHex);
            byte[] privateKeyBytes = Hex.decode(privateKeyHex);

            // 使用ECDSA签名
            byte[] signature = ecdsaSign(hash, privateKeyBytes);

            return Hex.toHexString(signature);

        } catch (Exception e) {
            log.error("签名哈希失败: {}", e.getMessage());
            throw new RuntimeException("签名哈希失败: " + e.getMessage(), e);
        }
    }

    /**
     * ECDSA签名实现
     * 使用BouncyCastle库实现标准的secp256k1 ECDSA签名
     * 符合TRON网络的签名要求
     */
    private static byte[] ecdsaSign(byte[] hash, byte[] privateKey) {
        try {
            log.debug("开始ECDSA签名，哈希长度: {}, 私钥长度: {}", hash.length, privateKey.length);

            // 验证输入参数
            if (hash.length != 32) {
                throw new IllegalArgumentException("哈希长度必须是32字节");
            }
            if (privateKey.length != 32) {
                throw new IllegalArgumentException("私钥长度必须是32字节");
            }

            // 创建私钥参数
            BigInteger privKey = new BigInteger(1, privateKey);
            ECPrivateKeyParameters privateKeyParams = new ECPrivateKeyParameters(privKey, DOMAIN_PARAMS);

            // 创建ECDSA签名器，使用RFC 6979确定性k值生成
            ECDSASigner signer = new ECDSASigner(new HMacDSAKCalculator(new SHA256Digest()));
            signer.init(true, privateKeyParams);

            // 执行签名
            BigInteger[] signature = signer.generateSignature(hash);
            BigInteger r = signature[0];
            BigInteger s = signature[1];

            // 确保s在低半部分（canonical signature）
            BigInteger halfN = DOMAIN_PARAMS.getN().shiftRight(1);
            if (s.compareTo(halfN) > 0) {
                s = DOMAIN_PARAMS.getN().subtract(s);
            }

            // 计算recovery id
            int recoveryId = calculateRecoveryId(r, s, hash, privateKey);

            // 转换为32字节数组并组合签名
            byte[] rBytes32 = toBytes32(r);
            byte[] sBytes32 = toBytes32(s);
            byte[] finalSignature = new byte[65];
            System.arraycopy(rBytes32, 0, finalSignature, 0, 32);
            System.arraycopy(sBytes32, 0, finalSignature, 32, 32);
            finalSignature[64] = (byte) recoveryId;

            // 验证签名是否正确
            if (!verifySignature(finalSignature, hash, privateKey)) {
                throw new RuntimeException("生成的签名验证失败，请检查签名算法");
            }

            return finalSignature;

        } catch (Exception e) {
            log.debug("ECDSA签名失败: {}", e.getMessage());
            throw new RuntimeException("ECDSA签名失败: " + e.getMessage(), e);
        }
    }

    /**
     * 计算recovery id
     * 用于从签名中恢复公钥
     */
    private static int calculateRecoveryId(BigInteger r, BigInteger s, byte[] hash, byte[] privateKey) {
        try {
            // 使用专用加密日志器记录关键步骤
            log.debug("开始计算recovery id");

            // 从私钥计算公钥
            BigInteger privKey = new BigInteger(1, privateKey);
            ECPoint publicKeyPoint = DOMAIN_PARAMS.getG().multiply(privKey).normalize();
            log.trace("原始公钥: x={}, y={}",
                publicKeyPoint.getAffineXCoord().toString(),
                publicKeyPoint.getAffineYCoord().toString());

            // 尝试不同的recovery id值（0-3）
            for (int recoveryId = 0; recoveryId < 4; recoveryId++) {
                log.trace("尝试recovery id: {}", recoveryId);

                ECPoint recoveredPoint = recoverPublicKey(r, s, hash, recoveryId);
                if (recoveredPoint != null) {
                    log.trace("恢复的公钥: x={}, y={}",
                        recoveredPoint.getAffineXCoord().toString(),
                        recoveredPoint.getAffineYCoord().toString());

                    // 比较公钥点
                    if (recoveredPoint.equals(publicKeyPoint)) {
                        log.debug("找到匹配的recovery id: {}", recoveryId);
                        return recoveryId;
                    }
                } else {
                    log.debug("recovery id {} 恢复公钥失败", recoveryId);
                }
            }

            // 如果找不到匹配的recovery id，这是一个严重错误
            String errorMsg = String.format("无法找到匹配的recovery id，签名可能有问题。r=%s, s=%s",
                r.toString(16), s.toString(16));
            log.error(errorMsg);
            throw new RuntimeException(errorMsg);

        } catch (Exception e) {
            log.error("计算recovery id失败: {}", e.getMessage());
            throw new RuntimeException("计算recovery id失败: " + e.getMessage(), e);
        }
    }

    /**
     * 从签名恢复公钥（完整实现）
     */
    private static ECPoint recoverPublicKey(BigInteger r, BigInteger s, byte[] hash, int recoveryId) {
        try {
            log.debug("开始恢复公钥: r={}, s={}, recoveryId={}", r.toString(16), s.toString(16), recoveryId);

            // 验证recovery id范围
            if (recoveryId < 0 || recoveryId > 3) {
                log.debug("无效的recovery id: {}", recoveryId);
                return null;
            }

            // 计算x坐标
            BigInteger x = r;
            if ((recoveryId & 2) != 0) {
                x = x.add(DOMAIN_PARAMS.getN());
                log.debug("调整x坐标: {}", x.toString(16));
            }

            // 验证x坐标是否在有效范围内
            if (x.compareTo(DOMAIN_PARAMS.getCurve().getField().getCharacteristic()) >= 0) {
                log.debug("x坐标超出范围: {}", x.toString(16));
                return null;
            }

            // 构造椭圆曲线点R
            byte[] xBytes = toBytes32(x);
            byte[] compressedPoint = new byte[33];
            compressedPoint[0] = (byte) (0x02 + (recoveryId & 1)); // 压缩格式前缀
            System.arraycopy(xBytes, 0, compressedPoint, 1, 32);

            ECPoint R;
            try {
                R = DOMAIN_PARAMS.getCurve().decodePoint(compressedPoint);
                log.debug("成功构造点R: x={}, y={}", R.getAffineXCoord().toString(), R.getAffineYCoord().toString());
            } catch (Exception e) {
                log.debug("构造点R失败: {}", e.getMessage());
                return null;
            }

            // 计算e = hash转换为BigInteger
            BigInteger e = new BigInteger(1, hash);
            log.debug("哈希转换为BigInteger: {}", e.toString(16));

            // 计算r的模逆
            BigInteger rInv;
            try {
                rInv = r.modInverse(DOMAIN_PARAMS.getN());
                log.debug("r的模逆: {}", rInv.toString(16));
            } catch (Exception ex) {
                log.debug("计算r的模逆失败: {}", ex.getMessage());
                return null;
            }

            // 计算公钥: Q = r^(-1) * (s * R - e * G)
            ECPoint sR = R.multiply(s);
            ECPoint eG = DOMAIN_PARAMS.getG().multiply(e);
            ECPoint Q = sR.subtract(eG).multiply(rInv).normalize();

            log.debug("恢复的公钥: x={}, y={}", Q.getAffineXCoord().toString(), Q.getAffineYCoord().toString());

            return Q;

        } catch (Exception e) {
            log.debug("公钥恢复失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 将BigInteger转换为32字节数组
     */
    private static byte[] toBytes32(BigInteger value) {
        byte[] bytes = value.toByteArray();

        if (bytes.length == 32) {
            return bytes;
        } else if (bytes.length > 32) {
            // 移除前导零字节
            return Arrays.copyOfRange(bytes, bytes.length - 32, bytes.length);
        } else {
            // 补充前导零
            byte[] result = new byte[32];
            System.arraycopy(bytes, 0, result, 32 - bytes.length, bytes.length);
            return result;
        }
    }

    /**
     * 验证签名是否正确
     */
    private static boolean verifySignature(byte[] signature, byte[] hash, byte[] privateKey) {
        try {
            log.debug("开始验证签名");

            // 解析签名
            if (signature.length != 65) {
                log.error("签名长度错误: {}", signature.length);
                return false;
            }

            byte[] rBytes = Arrays.copyOfRange(signature, 0, 32);
            byte[] sBytes = Arrays.copyOfRange(signature, 32, 64);
            int recoveryId = signature[64] & 0xFF;

            BigInteger r = new BigInteger(1, rBytes);
            BigInteger s = new BigInteger(1, sBytes);

            log.debug("解析签名: r={}, s={}, recoveryId={}", r.toString(16), s.toString(16), recoveryId);

            // 从签名恢复公钥
            ECPoint recoveredPublicKey = recoverPublicKey(r, s, hash, recoveryId);
            if (recoveredPublicKey == null) {
                log.error("无法从签名恢复公钥");
                return false;
            }

            // 从私钥计算公钥
            BigInteger privKey = new BigInteger(1, privateKey);
            ECPoint expectedPublicKey = DOMAIN_PARAMS.getG().multiply(privKey).normalize();

            // 比较公钥
            boolean isValid = recoveredPublicKey.equals(expectedPublicKey);
            log.debug("签名验证结果: {}", isValid);

            return isValid;

        } catch (Exception e) {
            log.error("签名验证失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * SHA256哈希
     */
    private static byte[] sha256(byte[] input) {
        try {
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            return digest.digest(input);
        } catch (Exception e) {
            throw new RuntimeException("SHA256计算失败", e);
        }
    }

    /**
     * 验证私钥格式
     */
    public static boolean isValidPrivateKey(String privateKeyHex) {
        try {
            if (privateKeyHex == null || privateKeyHex.trim().isEmpty()) {
                return false;
            }

            // 移除0x前缀
            if (privateKeyHex.startsWith("0x")) {
                privateKeyHex = privateKeyHex.substring(2);
            }

            // 检查长度
            if (privateKeyHex.length() != 64) {
                return false;
            }

            // 检查是否为有效的十六进制
            new BigInteger(privateKeyHex, 16);

            return true;

        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 从私钥生成TRON地址（使用正确的算法）
     */
    private static String generateAddressFromPrivateKey(String privateKeyHex) {
        try {
            log.debug("开始从私钥生成TRON地址: {}", privateKeyHex);

            // 将私钥转换为字节数组
            byte[] privateKeyBytes = Hex.decode(privateKeyHex);
            BigInteger privateKeyBigInt = new BigInteger(1, privateKeyBytes);
            log.debug("私钥转换完成，长度: {}", privateKeyBytes.length);

            // 生成未压缩公钥点
            ECPoint publicKeyPoint = DOMAIN_PARAMS.getG().multiply(privateKeyBigInt).normalize();
            byte[] publicKeyBytes = publicKeyPoint.getEncoded(false); // 未压缩格式
            log.debug("未压缩公钥生成完成，长度: {}", publicKeyBytes.length);

            // 去掉前缀"04"
            byte[] publicKeyWithoutPrefix = new byte[64];
            System.arraycopy(publicKeyBytes, 1, publicKeyWithoutPrefix, 0, 64);
            log.debug("去掉04前缀完成，长度: {}", publicKeyWithoutPrefix.length);

            // 使用正确的Keccak256哈希
            byte[] keccakHash = keccak256Correct(publicKeyWithoutPrefix);
            log.debug("Keccak256哈希完成，长度: {}", keccakHash.length);

            // 取后20字节作为地址
            byte[] addressBytes = new byte[20];
            System.arraycopy(keccakHash, 12, addressBytes, 0, 20);
            log.debug("地址字节提取完成: {}", Hex.toHexString(addressBytes));

            // 添加TRON地址前缀0x41
            byte[] tronAddressBytes = new byte[21];
            tronAddressBytes[0] = 0x41;
            System.arraycopy(addressBytes, 0, tronAddressBytes, 1, 20);
            log.debug("TRON前缀添加完成: {}", Hex.toHexString(tronAddressBytes));

            // 使用正确的Base58Check编码
            return encodeBase58CheckCorrect(tronAddressBytes);

        } catch (Exception e) {
            log.error("从私钥生成地址失败: {}", e.getMessage(), e);
            throw new RuntimeException("从私钥生成地址失败: " + e.getMessage(), e);
        }
    }

    /**
     * 从交易中提取发送方地址
     */
    private static String extractOwnerAddressFromTransaction(JsonNode transaction) {
        try {
            log.debug("开始从交易中提取发送方地址...");
            log.debug("交易数据结构: {}", transaction.toString());

            String ownerAddress = null;

            // 方法1: 从raw_data.contract[0].parameter.value.owner_address提取
            ownerAddress = extractFromRawDataContract(transaction);
            if (ownerAddress != null) {
                log.debug("从raw_data.contract路径提取到地址: {}", ownerAddress);
                return convertAddressToBase58(ownerAddress);
            }

            // 方法2: 从raw_data.contract[0].parameter.value.from_address提取 (某些交易类型)
            ownerAddress = extractFromRawDataContractFromAddress(transaction);
            if (ownerAddress != null) {
                log.debug("从raw_data.contract.from_address路径提取到地址: {}", ownerAddress);
                return convertAddressToBase58(ownerAddress);
            }

            // 方法3: 从顶层字段提取
            ownerAddress = extractFromTopLevel(transaction);
            if (ownerAddress != null) {
                log.debug("从顶层字段提取到地址: {}", ownerAddress);
                return convertAddressToBase58(ownerAddress);
            }

            // 方法4: 从raw_data直接提取owner_address字段
            ownerAddress = extractFromRawDataDirect(transaction);
            if (ownerAddress != null) {
                log.debug("从raw_data直接提取到地址: {}", ownerAddress);
                return convertAddressToBase58(ownerAddress);
            }

            // 如果所有方法都失败，记录详细信息
            log.error("所有提取方法都失败，交易结构可能不符合预期");
            logTransactionStructure(transaction);

            throw new IllegalArgumentException("无法从交易中提取发送方地址 - 请检查交易数据结构");

        } catch (Exception e) {
            log.error("提取交易发送方地址失败，原始错误: {}", e.getMessage());
            throw new RuntimeException("提取交易发送方地址失败: " + e.getMessage(), e);
        }
    }

    /**
     * 方法1: 从raw_data.contract[0].parameter.value.owner_address提取
     */
    private static String extractFromRawDataContract(JsonNode transaction) {
        try {
            if (transaction.has("raw_data")) {
                JsonNode rawData = transaction.get("raw_data");
                if (rawData.has("contract") && rawData.get("contract").isArray() && rawData.get("contract").size() > 0) {
                    JsonNode contract = rawData.get("contract").get(0);
                    if (contract.has("parameter")) {
                        JsonNode parameter = contract.get("parameter");
                        if (parameter.has("value")) {
                            JsonNode value = parameter.get("value");
                            if (value.has("owner_address")) {
                                return value.get("owner_address").asText();
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.debug("方法1提取失败: {}", e.getMessage());
        }
        return null;
    }

    /**
     * 方法2: 从raw_data.contract[0].parameter.value.from_address提取
     */
    private static String extractFromRawDataContractFromAddress(JsonNode transaction) {
        try {
            if (transaction.has("raw_data")) {
                JsonNode rawData = transaction.get("raw_data");
                if (rawData.has("contract") && rawData.get("contract").isArray() && rawData.get("contract").size() > 0) {
                    JsonNode contract = rawData.get("contract").get(0);
                    if (contract.has("parameter")) {
                        JsonNode parameter = contract.get("parameter");
                        if (parameter.has("value")) {
                            JsonNode value = parameter.get("value");
                            if (value.has("from_address")) {
                                return value.get("from_address").asText();
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.debug("方法2提取失败: {}", e.getMessage());
        }
        return null;
    }

    /**
     * 方法3: 从顶层字段提取
     */
    private static String extractFromTopLevel(JsonNode transaction) {
        try {
            // 检查顶层是否有owner_address字段
            if (transaction.has("owner_address")) {
                return transaction.get("owner_address").asText();
            }
            // 检查顶层是否有from_address字段
            if (transaction.has("from_address")) {
                return transaction.get("from_address").asText();
            }
        } catch (Exception e) {
            log.debug("方法3提取失败: {}", e.getMessage());
        }
        return null;
    }

    /**
     * 方法4: 从raw_data直接提取
     */
    private static String extractFromRawDataDirect(JsonNode transaction) {
        try {
            if (transaction.has("raw_data")) {
                JsonNode rawData = transaction.get("raw_data");
                if (rawData.has("owner_address")) {
                    return rawData.get("owner_address").asText();
                }
                if (rawData.has("from_address")) {
                    return rawData.get("from_address").asText();
                }
            }
        } catch (Exception e) {
            log.debug("方法4提取失败: {}", e.getMessage());
        }
        return null;
    }

    /**
     * 记录交易结构用于调试（仅在DEBUG级别启用）
     */
    private static void logTransactionStructure(JsonNode transaction) {
        if (!log.isDebugEnabled()) {
            return;
        }

        try {
            log.debug("交易数据结构分析 - 顶层字段: {}", transaction.fieldNames());
        } catch (Exception e) {
            log.debug("记录交易结构失败: {}", e.getMessage());
        }
    }

    /**
     * 将地址转换为Base58格式
     * 支持十六进制和Base58格式的输入
     */
    private static String convertAddressToBase58(String address) {
        if (address == null || address.trim().isEmpty()) {
            throw new IllegalArgumentException("地址不能为空");
        }

        String normalizedAddress = address.trim();

        // 情况1: 十六进制格式 (42字符，以41开头)
        if (isHexAddress(normalizedAddress)) {
            return convertHexToBase58(normalizedAddress);
        }

        // 情况2: Base58格式 (34字符，以T开头)
        if (isBase58Address(normalizedAddress)) {
            return normalizedAddress;
        }

        // 情况3: 不支持的格式
        throw new IllegalArgumentException(
            String.format("不支持的地址格式: %s (长度: %d)", normalizedAddress, normalizedAddress.length()));
    }

    /**
     * 检查是否为有效的十六进制地址格式
     */
    private static boolean isHexAddress(String address) {
        if (address.length() != 42) {
            return false;
        }

        String lowerAddress = address.toLowerCase();
        if (!lowerAddress.startsWith("41")) {
            return false;
        }

        // 检查是否为有效的十六进制字符
        try {
            Hex.decode(lowerAddress);
            return true;
        } catch (Exception e) {
            log.debug("十六进制字符验证失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 检查是否为有效的Base58地址格式
     */
    private static boolean isBase58Address(String address) {
        return address.length() == 34 && address.startsWith("T");
    }

    /**
     * 将十六进制地址转换为Base58格式
     */
    private static String convertHexToBase58(String hexAddress) {
        try {
            // 统一转换为小写，避免大小写问题
            String normalizedHex = hexAddress.toLowerCase();
            log.debug("转换十六进制地址: {}", normalizedHex);

            // 解码为字节数组
            byte[] addressBytes = Hex.decode(normalizedHex);
            log.debug("解码后字节数组长度: {}", addressBytes.length);

            // 验证字节数组长度
            if (addressBytes.length != 21) {
                throw new IllegalArgumentException(
                    String.format("解码后字节长度错误: %d (期望21)", addressBytes.length));
            }

            // 验证TRON前缀
            if (addressBytes[0] != 0x41) {
                throw new IllegalArgumentException(
                    String.format("TRON前缀错误: 0x%02x (期望0x41)", addressBytes[0]));
            }

            // 使用正确的Base58Check编码
            String base58Address = encodeBase58CheckCorrect(addressBytes);
            log.debug("转换后的Base58地址: {}", base58Address);

            return base58Address;

        } catch (Exception e) {
            log.error("十六进制地址转换失败: {}, 错误: {}", hexAddress, e.getMessage());
            throw new RuntimeException("十六进制地址转换失败: " + e.getMessage(), e);
        }
    }

    /**
     * Keccak256哈希计算（旧版本，已弃用）
     * @deprecated 使用 keccak256Correct 替代
     */
    @Deprecated
    private static byte[] keccak256(byte[] input) {
        try {
            MessageDigest digest = MessageDigest.getInstance("KECCAK-256", "BC");
            return digest.digest(input);
        } catch (Exception e) {
            throw new RuntimeException("Keccak256哈希计算失败: " + e.getMessage(), e);
        }
    }

    /**
     * 正确的Keccak256哈希计算
     */
    private static byte[] keccak256Correct(byte[] input) {
        org.bouncycastle.crypto.digests.KeccakDigest digest = new org.bouncycastle.crypto.digests.KeccakDigest(256);
        digest.update(input, 0, input.length);
        byte[] result = new byte[digest.getDigestSize()];
        digest.doFinal(result, 0);
        return result;
    }

    /**
     * 正确的Base58Check编码
     */
    private static String encodeBase58CheckCorrect(byte[] input) {
        try {
            // 双重SHA256计算校验和
            byte[] sha256_1 = sha256(input);
            byte[] sha256_2 = sha256(sha256_1);

            // 取前4字节作为校验和
            byte[] checksum = new byte[4];
            System.arraycopy(sha256_2, 0, checksum, 0, 4);

            // 组合地址和校验和
            byte[] fullAddress = new byte[input.length + 4];
            System.arraycopy(input, 0, fullAddress, 0, input.length);
            System.arraycopy(checksum, 0, fullAddress, input.length, 4);

            // Base58编码
            return base58EncodeCorrect(fullAddress);

        } catch (Exception e) {
            throw new RuntimeException("Base58Check编码失败: " + e.getMessage(), e);
        }
    }

    /**
     * 正确的Base58编码
     */
    private static String base58EncodeCorrect(byte[] input) {
        final String BASE58_ALPHABET = "123456789ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijkmnopqrstuvwxyz";

        if (input.length == 0) {
            return "";
        }

        // 计算前导零的数量
        int leadingZeros = 0;
        while (leadingZeros < input.length && input[leadingZeros] == 0) {
            leadingZeros++;
        }

        // 复制输入数组
        byte[] temp = java.util.Arrays.copyOf(input, input.length);

        // 编码
        StringBuilder result = new StringBuilder();
        for (int i = leadingZeros; i < temp.length; ) {
            int remainder = 0;
            for (int j = i; j < temp.length; j++) {
                int num = (remainder << 8) + (temp[j] & 0xFF);
                temp[j] = (byte) (num / 58);
                remainder = num % 58;
            }
            result.append(BASE58_ALPHABET.charAt(remainder));

            // 跳过前导零
            while (i < temp.length && temp[i] == 0) {
                i++;
            }
        }

        // 添加前导1
        for (int i = 0; i < leadingZeros; i++) {
            result.append('1');
        }

        return result.reverse().toString();
    }


    /**
     * 从私钥生成TRON地址（公共API）
     *
     * @param privateKeyHex 私钥十六进制字符串
     * @return TRON地址（Base58格式）
     */
    public static String getAddressFromPrivateKey(String privateKeyHex) {
        try {
            // 验证私钥格式
            if (!isValidPrivateKey(privateKeyHex)) {
                throw new IllegalArgumentException("无效的私钥格式");
            }

            // 移除0x前缀
            if (privateKeyHex.startsWith("0x")) {
                privateKeyHex = privateKeyHex.substring(2);
            }

            // 调用内部实现
            return generateAddressFromPrivateKey(privateKeyHex);

        } catch (Exception e) {
            log.error("从私钥生成地址失败: {}", e.getMessage());
            throw new RuntimeException("从私钥生成地址失败: " + e.getMessage(), e);
        }
    }

}
