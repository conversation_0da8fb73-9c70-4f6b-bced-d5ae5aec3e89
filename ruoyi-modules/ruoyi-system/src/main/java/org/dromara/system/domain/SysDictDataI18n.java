package org.dromara.system.domain;

import com.baomidou.mybatisplus.annotation.*;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.Data;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.system.domain.vo.SysDictDataI18nVo;

import java.util.Date;

/**
 * 字典数据多语言扩展表
 */
@Data
@AutoMappers({
    @AutoMapper(target = SysDictDataI18nVo.class)
})
@TableName("sys_dict_data_i18n")
public class SysDictDataI18n {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 关联字典编码
     */
    private Long dictCode;
    /**
     * 租户编号
     */
    private String tenantId;
    /**
     * 语言标签(如zh-CN, en-US)
     */
    private String languageTag;
    /**
     * 多语言字典标签
     */
    private String dictLabel;
    /**
     * 多语言字典键值
     */
    private String dictValue;
    /**
     * 多语言备注
     */
    private String remark;
    /**
     * 创建者
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createBy;
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;
    /**
     * 更新者
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updateBy;
    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

}
