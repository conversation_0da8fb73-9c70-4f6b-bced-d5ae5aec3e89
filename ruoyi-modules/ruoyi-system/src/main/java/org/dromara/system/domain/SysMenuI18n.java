package org.dromara.system.domain;

import com.baomidou.mybatisplus.annotation.*;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.Data;
import org.dromara.system.domain.vo.SysDictDataI18nVo;
import org.dromara.system.domain.vo.SysMenuI18nVo;

import java.util.Date;

/**
 * 菜单多语言扩展表
 */
@Data
@AutoMappers({
    @AutoMapper(target = SysMenuI18nVo.class)
})
@TableName("sys_menu_i18n")
public class SysMenuI18n {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 菜单ID
     */
    private Long menuId;
    /**
     * 语言标签(如zh-CN, en-US)
     */
    private String languageTag;
    /**
     * 多语言菜单标签
     */
    private String menuLabel;
    /**
     * 多语言备注
     */
    private String remark;
    /**
     * 创建者
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createBy;
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;
    /**
     * 更新者
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updateBy;
    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

}
