package org.dromara.system.domain.vo;

import lombok.Data;
import org.dromara.common.core.utils.StringUtils;

import java.io.Serial;
import java.io.Serializable;
import java.util.Map;

/**
 * 路由显示信息
 *
 * <AUTHOR>
 */

@Data
public class MetaVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 设置该路由在侧边栏和面包屑中展示的名字
     */
    private String title;

    /**
     * 设置该路由的图标，对应路径src/assets/icons/svg
     */
    private String icon;

    /**
     * 设置为true，则不会被 <keep-alive>缓存
     */
    private Boolean noCache;

    /**
     * 内链地址（http(s)://开头）
     */
    private String link;

    /**
     * 多语言
     */
    private Map<String, String> multiLanguage;

    public MetaVo(String title, String icon, Map<String, String> multiLanguage) {
        this.title = title;
        this.icon = icon;
        this.multiLanguage = multiLanguage;
    }

    public MetaVo(String title, String icon, Boolean noCache) {
        this.title = title;
        this.icon = icon;
        this.noCache = noCache;
    }

    public MetaVo(String title, String icon, String link, Map<String, String> multiLanguage) {
        this.title = title;
        this.icon = icon;
        this.link = link;
        this.multiLanguage = multiLanguage;
    }

    public MetaVo(String title, String icon, Boolean noCache, String link, Map<String, String> multiLanguage) {
        this.title = title;
        this.icon = icon;
        this.noCache = noCache;
        if (StringUtils.ishttp(link)) {
            this.link = link;
        }
        this.multiLanguage = multiLanguage;
    }

}
