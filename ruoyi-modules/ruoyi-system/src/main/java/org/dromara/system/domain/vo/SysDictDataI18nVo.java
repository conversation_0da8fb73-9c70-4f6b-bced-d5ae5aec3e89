package org.dromara.system.domain.vo;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.util.Date;

@Data
public class SysDictDataI18nVo {

    private Long id;
    /**
     * 关联字典编码
     */
    private Long dictCode;
    /**
     * 租户编号
     */
    private String tenantId;
    /**
     * 语言标签(如zh-CN, en-US)
     */
    private String languageTag;
    /**
     * 多语言字典标签
     */
    private String dictLabel;
    /**
     * 多语言字典键值
     */
    private String dictValue;
    /**
     * 多语言备注
     */
    private String remark;
    /**
     * 创建者
     */
    private Long createBy;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 更新者
     */
    private Long updateBy;
    /**
     * 更新时间
     */
    private Date updateTime;
}
