package org.dromara.system.domain.vo;

import lombok.Data;

import java.util.Date;

@Data
public class SysMenuI18nVo {

    /**
     * 主键ID
     */
    private Long id;
    /**
     * 菜单ID
     */
    private Long menuId;
    /**
     * 语言标签(如zh-CN, en-US)
     */
    private String languageTag;
    /**
     * 多语言菜单标签
     */
    private String menuLabel;
    /**
     * 多语言备注
     */
    private String remark;
    /**
     * 创建者
     */
    private Long createBy;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 更新者
     */
    private Long updateBy;
    /**
     * 更新时间
     */
    private Date updateTime;
}
