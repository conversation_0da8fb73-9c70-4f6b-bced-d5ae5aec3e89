package org.dromara.system.service.impl;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.constant.CacheNames;
import org.dromara.common.core.enums.LanguageEnum;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.ObjectUtils;
import org.dromara.common.core.utils.ServletUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.redis.utils.CacheUtils;
import org.dromara.common.redis.utils.RedisUtils;
import org.dromara.common.satoken.utils.LoginHelper;
import org.dromara.common.tenant.helper.TenantHelper;
import org.dromara.system.api.domain.vo.RemoteUserVo;
import org.dromara.system.domain.SysDictData;
import org.dromara.system.domain.SysDictDataI18n;
import org.dromara.system.domain.bo.SysDictDataBo;
import org.dromara.system.domain.vo.SysDictDataI18nVo;
import org.dromara.system.domain.vo.SysDictDataVo;
import org.dromara.system.mapper.SysDictDataI18nMapper;
import org.dromara.system.mapper.SysDictDataMapper;
import org.dromara.system.service.ISysDictDataService;
import org.springframework.cache.annotation.CachePut;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 字典 业务层处理
 *
 * <AUTHOR> Li
 */
@RequiredArgsConstructor
@Service
public class SysDictDataServiceImpl implements ISysDictDataService {

    private final SysDictDataMapper baseMapper;
    private final SysDictDataI18nMapper sysDictDataI18nMapper;

    @Override
    public TableDataInfo<SysDictDataVo> selectPageDictDataList(SysDictDataBo dictData, PageQuery pageQuery) {
        LambdaQueryWrapper<SysDictData> lqw = buildQueryWrapper(dictData);
        Page<SysDictDataVo> page = baseMapper.selectVoPage(pageQuery.build(), lqw);
        List<SysDictDataVo> voList = page.getRecords();
        if (!voList.isEmpty()) {
            List<Long> dictCodes = voList.stream()
                .map(SysDictDataVo::getDictCode)
                .toList();
            List<SysDictDataI18n> i18nList = sysDictDataI18nMapper.selectList(new LambdaQueryWrapper<SysDictDataI18n>()
                .in(SysDictDataI18n::getDictCode, dictCodes));
            if (!i18nList.isEmpty()){
                Map<Long, List<SysDictDataI18n>> map = i18nList.stream()
                    .collect(Collectors.groupingBy(SysDictDataI18n::getDictCode));
                for (SysDictDataVo vo : voList) {
                    List<SysDictDataI18n> list = map.get(vo.getDictCode()) == null ? new ArrayList<>() : map.get(vo.getDictCode());
                    Map<String, String> multiLanguage = new HashMap<>();
                    for (SysDictDataI18n i18n : list) {
                        multiLanguage.put(i18n.getLanguageTag(), i18n.getDictLabel());
                    }
                    vo.setMultiLanguage(multiLanguage);
                }
            }
        }
        return TableDataInfo.build(page);
    }

    /**
     * 根据条件分页查询字典数据
     *
     * @param dictData 字典数据信息
     * @return 字典数据集合信息
     */
    @Override
    public List<SysDictDataVo> selectDictDataList(SysDictDataBo dictData) {
        LambdaQueryWrapper<SysDictData> lqw = buildQueryWrapper(dictData);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SysDictData> buildQueryWrapper(SysDictDataBo bo) {
        LambdaQueryWrapper<SysDictData> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getDictSort() != null, SysDictData::getDictSort, bo.getDictSort());
        lqw.like(StringUtils.isNotBlank(bo.getDictLabel()), SysDictData::getDictLabel, bo.getDictLabel());
        lqw.eq(StringUtils.isNotBlank(bo.getDictType()), SysDictData::getDictType, bo.getDictType());
        lqw.orderByAsc(SysDictData::getDictSort, SysDictData::getDictCode);
        return lqw;
    }

    /**
     * 根据字典类型和字典键值查询字典数据信息
     *
     * @param dictType  字典类型
     * @param dictValue 字典键值
     * @return 字典标签
     */
    @Override
    public String selectDictLabel(String dictType, String dictValue) {
        return baseMapper.selectOne(new LambdaQueryWrapper<SysDictData>()
                .select(SysDictData::getDictLabel)
                .eq(SysDictData::getDictType, dictType)
                .eq(SysDictData::getDictValue, dictValue))
            .getDictLabel();
    }

    /**
     * 根据字典数据ID查询信息
     *
     * @param dictCode 字典数据ID
     * @return 字典数据
     */
    @Override
    public SysDictDataVo selectDictDataById(Long dictCode) {
        SysDictDataVo sysDictDataVo = baseMapper.selectVoById(dictCode);
        List<SysDictDataI18n> list = sysDictDataI18nMapper.selectList(new LambdaQueryWrapper<SysDictDataI18n>()
            .eq(SysDictDataI18n::getDictCode, dictCode));
        Map<String, String> map = new HashMap<>();
        if (!list.isEmpty()) {
            for (SysDictDataI18n i18n : list) {
                map.put(i18n.getLanguageTag(), i18n.getDictLabel());
            }
        }
        sysDictDataVo.setMultiLanguage(map);
        return sysDictDataVo;
    }

    /**
     * 批量删除字典数据信息
     *
     * @param dictCodes 需要删除的字典数据ID
     */
    @Override
    public void deleteDictDataByIds(Long[] dictCodes) {
        for (Long dictCode : dictCodes) {
            SysDictData data = baseMapper.selectById(dictCode);
            baseMapper.deleteById(dictCode);
            sysDictDataI18nMapper.delete(new LambdaQueryWrapper<SysDictDataI18n>()
                .eq(SysDictDataI18n::getDictCode, dictCode));
            CacheUtils.evict(CacheNames.SYS_DICT, data.getDictType());
        }
    }

    /**
     * 新增保存字典数据信息
     *
     * @param bo 字典数据信息
     * @return 结果
     */
    @CachePut(cacheNames = CacheNames.SYS_DICT, key = "#bo.dictType")
    @Override
    public List<SysDictDataVo> insertDictData(SysDictDataBo bo) {
        SysDictData data = MapstructUtils.convert(bo, SysDictData.class);
        int row = baseMapper.insert(data);
        if (row > 0) {
            if (MapUtil.isNotEmpty(bo.getMultiLanguage())){
                List<SysDictDataI18n> list = new ArrayList<>();
                bo.getMultiLanguage().forEach( (key,value) ->{
                    SysDictDataI18n  i18n = new SysDictDataI18n();
                    i18n.setDictCode(data.getDictCode());
                    i18n.setLanguageTag(key);
                    i18n.setDictLabel(value);
                    i18n.setDictValue(data.getDictValue());
                    list.add(i18n);
                });
                sysDictDataI18nMapper.insertBatch(list);
            }
            return selectDictDataByType(data.getDictType());
        }
        throw new ServiceException("操作失败");
    }

    /**
     * 修改保存字典数据信息
     *
     * @param bo 字典数据信息
     * @return 结果
     */
    @CachePut(cacheNames = CacheNames.SYS_DICT, key = "#bo.dictType")
    @Override
    public List<SysDictDataVo> updateDictData(SysDictDataBo bo) {
        SysDictData data = MapstructUtils.convert(bo, SysDictData.class);
        int row = baseMapper.updateById(data);
        if (row > 0) {
            if (MapUtil.isEmpty(bo.getMultiLanguage())) bo.setMultiLanguage(new HashMap<>());
            List<SysDictDataI18n> list = sysDictDataI18nMapper.selectList(new LambdaQueryWrapper<SysDictDataI18n>()
                .eq(SysDictDataI18n::getDictCode, data.getDictCode()));
            Map<String, SysDictDataI18n> i18nMap = list.stream().collect(Collectors.toMap(SysDictDataI18n::getLanguageTag, Function.identity()));

            List<SysDictDataI18n> insert = new ArrayList<>();
            List<Long> delList = new ArrayList<>();
            Map<String, String> multiLanguage = bo.getMultiLanguage();

            // key 集合
            Set<String> oldKeys = i18nMap.keySet();
            Set<String> newKeys = multiLanguage.keySet();

            // 获取新增 key
            Set<String> addedKeys = new HashSet<>(newKeys);
            addedKeys.removeAll(oldKeys);

            // 获取删除 key
            Set<String> removedKeys = new HashSet<>(oldKeys);
            removedKeys.removeAll(newKeys);

            // 获取交集 key
            Set<String> unchangedKeys = new HashSet<>(oldKeys);
            unchangedKeys.retainAll(newKeys);

            // 处理新增的语言
            for (String addedKey : addedKeys) {
                SysDictDataI18n  i18n = new SysDictDataI18n();
                i18n.setDictCode(data.getDictCode());
                i18n.setLanguageTag(addedKey);
                i18n.setDictLabel(multiLanguage.get(addedKey));
                i18n.setDictValue(data.getDictValue());
                insert.add(i18n);
            }

            // 修改存在的语言
            for (String unchangedKey : unchangedKeys) {
                SysDictDataI18n i18n = i18nMap.get(unchangedKey);
                i18n.setDictLabel(multiLanguage.get(unchangedKey));
                i18n.setDictValue(data.getDictValue());
                insert.add(i18n);
            }

            // 删除不要的语言
            for (String removedKey : removedKeys) {
                delList.add(i18nMap.get(removedKey).getId());
            }

            sysDictDataI18nMapper.deleteByIds(delList);
            sysDictDataI18nMapper.insertOrUpdateBatch(insert);

            return selectDictDataByType(data.getDictType());
        }
        throw new ServiceException("操作失败");
    }

    /**
     * 校验字典键值是否唯一
     *
     * @param dict 字典数据
     * @return 结果
     */
    @Override
    public boolean checkDictDataUnique(SysDictDataBo dict) {
        Long dictCode = ObjectUtils.notNull(dict.getDictCode(), -1L);
        SysDictData entity = baseMapper.selectOne(new LambdaQueryWrapper<SysDictData>()
            .eq(SysDictData::getDictType, dict.getDictType()).eq(SysDictData::getDictValue, dict.getDictValue()));
        if (ObjectUtil.isNotNull(entity) && !dictCode.equals(entity.getDictCode())) {
            return false;
        }
        return true;
    }

    /**
     * 根据字典类型查询字典数据列表
     * @param dictType
     * @return
     */
    @Override
    public List<SysDictDataVo> selectDictDataByType(String dictType) {
        List<SysDictDataVo> sysDictDataVos = baseMapper.selectVoList(
            new LambdaQueryWrapper<SysDictData>()
                .eq(SysDictData::getDictType, dictType)
                .orderByAsc(SysDictData::getDictSort));

        sysDictDataVos.forEach(sysDictDataVo -> {
            List<SysDictDataI18nVo> i18nVoList = sysDictDataI18nMapper.selectVoList(new LambdaQueryWrapper<SysDictDataI18n>()
                .eq(SysDictDataI18n::getDictCode, sysDictDataVo.getDictCode()));
            Map<String, String> multiLanguage = new HashMap<>();
            for (SysDictDataI18nVo i18nVo : i18nVoList) {
                multiLanguage.put(i18nVo.getLanguageTag(), i18nVo.getDictLabel());
            }
            sysDictDataVo.setMultiLanguage(multiLanguage);
        });
        return sysDictDataVos;
    }

    /**
     * 根据类型和键值获取对应语言的标签
     * @param dictType
     * @param dictValue
     * @return
     */
    @Override
    public String getLabel(String dictType, String dictValue) {
        String language = Optional.ofNullable(ServletUtils.getRequest())
            .map(req -> req.getHeader("Accept-Language"))
            .orElse(LanguageEnum.ENGLISH.getValue()); // 默认英文

        String label = "";

        Map<String, List<SysDictDataVo>> dictMap = RedisUtils.getCacheMap( TenantHelper.getTenantId() + ":" + CacheNames.SYS_DICT);
        List<SysDictDataVo> dictList = dictMap.get(dictType);

        if (dictList == null) {
            // 缓存不存在时回退到数据库逻辑
            dictList = selectDictDataByType(dictType);
            CacheUtils.put(CacheNames.SYS_DICT, dictType, dictList);
        }

        for (SysDictDataVo sysDictDataVo : dictList) {
            if (sysDictDataVo.getDictValue().equals(dictValue)) {
                // 获取对应语言的标签
                if (LanguageEnum.ENGLISH.getValue().equals(language)) {
                    label = sysDictDataVo.getDictLabel();
                } else {
                    label = sysDictDataVo.getMultiLanguage().getOrDefault(language, sysDictDataVo.getDictLabel());
                }
                break;
            }
        }
        return label;
    }

}
