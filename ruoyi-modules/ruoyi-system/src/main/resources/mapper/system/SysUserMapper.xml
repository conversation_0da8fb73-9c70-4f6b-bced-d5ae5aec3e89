<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.system.mapper.SysUserMapper">

    <resultMap type="org.dromara.system.domain.vo.SysUserVo" id="SysUserResult">
        <id property="userId" column="user_id"/>
    </resultMap>
    <resultMap type="org.dromara.system.domain.vo.SysUserExportVo" id="SysUserExportResult">
        <id property="userId" column="user_id"/>
    </resultMap>

    <select id="selectPageUserList" resultMap="SysUserResult">
        select
        <if test="ew.getSqlSelect != null">
            ${ew.getSqlSelect}
        </if>
        <if test="ew.getSqlSelect == null">
            u.user_id, u.dept_id, u.uuid, u.nick_name, u.user_name, u.email, u.country_code, u.avatar, u.google_secret_key, u.phonenumber, u.sex,
            u.status, u.del_flag, u.login_ip, u.login_date, u.location, u.os, u.device, u.create_by, u.create_time, u.remark,
            u.invite_code, u.login_limit_expired, u.trade_limit_expired, u.language
        </if>
        from sys_user u
        ${ew.getCustomSqlSegment}
    </select>

    <select id="selectUserList" resultMap="SysUserResult">
        select
        <if test="ew.getSqlSelect != null">
            ${ew.getSqlSelect}
        </if>
        <if test="ew.getSqlSelect == null">
            u.user_id, u.dept_id, u.uuid, u.nick_name, u.user_name, u.email, u.country_code, u.avatar, u.google_secret_key, u.phonenumber, u.sex,
            u.status, u.del_flag, u.login_ip, u.login_date, u.location, u.os, u.device, u.create_by, u.create_time, u.remark,
            u.invite_code, u.login_limit_expired, u.trade_limit_expired, u.language
        </if>
        from sys_user u
        ${ew.getCustomSqlSegment}
    </select>

    <select id="selectUserExportList" resultMap="SysUserExportResult">
        select u.user_id, u.dept_id, u.uuid, u.nick_name, u.user_name, u.email, u.country_code, u.avatar, u.phonenumber, u.sex,
               u.status, u.del_flag, u.login_ip, u.login_date, u.location, u.create_by, u.create_time, u.remark,
               u.invite_code, u.language, d.dept_name, d.leader, u1.user_name as leaderName
        from sys_user u
                 left join sys_dept d on u.dept_id = d.dept_id
                 left join sys_user u1 on u1.user_id = d.leader
            ${ew.getCustomSqlSegment}
    </select>

    <select id="selectAllocatedList" resultMap="SysUserResult">
        select distinct u.user_id, u.dept_id, u.user_name, u.nick_name, u.email, u.phonenumber, u.status, u.create_time
        from sys_user u
             left join sys_dept d on u.dept_id = d.dept_id
             left join sys_user_role sur on u.user_id = sur.user_id
             left join sys_role r on r.role_id = sur.role_id
        ${ew.getCustomSqlSegment}
    </select>

    <select id="selectUnallocatedList" resultMap="SysUserResult">
        select distinct u.user_id, u.dept_id, u.user_name, u.nick_name, u.email, u.phonenumber, u.status, u.create_time
        from sys_user u
             left join sys_dept d on u.dept_id = d.dept_id
             left join sys_user_role sur on u.user_id = sur.user_id
             left join sys_role r on r.role_id = sur.role_id
        ${ew.getCustomSqlSegment}
    </select>

    <select id="countUserById" resultType="Long">
        select count(*) from sys_user where del_flag = '0' and user_id = #{userId}
    </select>

    <select id="selectUserPageByParam" resultMap="SysUserResult">
        select user_id, user_name as userName, nick_name as nickName, phonenumber, status,
        create_time as createTime, login_limit_expired as loginLimitExpired, trade_limit_expired as tradeLimitExpired
        from sys_user
        where user_type != 'sys_user' and del_flag = '0'
        <if test="bo.status != null and bo.status != ''">
            and status = #{bo.status}
        </if>
        <if test="bo.startDate != null and bo.startDate != ''">
            and create_time &gt;= #{bo.startDate}
        </if>
        <if test="bo.endDate != null and bo.endDate != ''">
            and create_time &lt;= #{bo.endDate}
        </if>
        <if test='bo.limitFlag != null and bo.limitFlag != "" and bo.limitFlag == "Y"'>
            and (login_limit_expired > now() or trade_limit_expired > now())
        </if>
        <if test='bo.limitFlag != null and bo.limitFlag != "" and bo.limitFlag == "N"'>
            and (login_limit_expired &lt; now() or trade_limit_expired &lt; now()
                or login_limit_expired is null or trade_limit_expired is null)
        </if>
        <if test="bo.userName != null and bo.userName != ''">
            and user_name like concat('%', #{bo.userName}, '%')
        </if>
        <if test="bo.nickName != null and bo.nickName != ''">
            and nick_name like concat('%', #{bo.nickName}, '%')
        </if>
        <if test="bo.ids != null and bo.ids.size() > 0">
            and user_id in
            <foreach collection="bo.ids" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        order by create_time desc
    </select>


</mapper>
